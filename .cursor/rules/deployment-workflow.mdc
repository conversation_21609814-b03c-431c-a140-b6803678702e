---
description: Deployment Workflow
globs: 
alwaysApply: false
---
# Deployment Workflow

## Deployment Environments

The application supports multiple deployment environments:
- **Test**: For testing purposes
- **Gray**: For canary/pre-production deployment
- **Production**: For production environment

## Deployment Process

1. The deployment is managed via [deploy.sh](mdc:deploy.sh) script which:
   - Authenticates with Google Artifact Registry
   - Installs dependencies
   - Builds the project for production
   - Starts the application using PM2 with the appropriate environment config

2. PM2 Configuration:
   - [server/processes.json](mdc:server/processes.json) contains PM2 process definitions for both test and production environments
   - Production runs on port 4090
   - Test environment runs on port 4091

## Nginx Configuration

The application uses Nginx as a reverse proxy with separate configs for each environment:
- [nginx_test.conf](mdc:nginx_test.conf): Test environment configuration
- [nginx_gray.conf](mdc:nginx_gray.conf): Gray/Canary environment configuration
- [nginx_prod.conf](mdc:nginx_prod.conf): Production environment configuration

## Continuous Integration

The repository uses GitLab CI/CD pipeline defined in [.gitlab-ci.yml](mdc:.gitlab-ci.yml).

## Versioning

Version control is managed through [publish.json](mdc:publish.json) which defines version tags for different branches:
- `main`/`master` branch: v0.0.0 tag
- `gray` branch: g0.0.0 tag
