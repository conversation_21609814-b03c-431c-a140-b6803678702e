---
description: Project Structure Guide
globs: 
alwaysApply: false
---
# Project Structure Guide

This is a Koa-based TypeScript API service for reporting functionality. The project follows a standard MVC architecture.

## Main Directories

- [server/src/index.ts](mdc:server/src/index.ts): Main entry point for the application
- [server/src/routers/](mdc:server/src/routers): API route definitions
- [server/src/controllers/](mdc:server/src/controllers): Request handlers for API endpoints
- [server/src/services/](mdc:server/src/services): Business logic layer
- [server/src/models/](mdc:server/src/models): Data access layer
- [server/src/middleware/](mdc:server/src/middleware): Koa middleware functions
- [server/src/db/](mdc:server/src/db): Database connections and utilities
- [server/src/config/](mdc:server/src/config): Configuration files for different environments
- [server/src/utils/](mdc:server/src/utils): Utility functions and helpers
- [server/src/types/](mdc:server/src/types): TypeScript type definitions
- [server/src/schema/](mdc:server/src/schema): Validation schemas
- [server/src/constants/](mdc:server/src/constants): Constant values

## Key Features

The application provides reporting API endpoints under the reporting-api directory structure with modules for:
- Report generation and download
- Partner-related reporting
- Demand-side reporting
- SSP-related reporting
