---
description: Architecture and Technologies
globs: 
alwaysApply: false
---
# Architecture and Technologies

## Tech Stack

This project is a reporting API service built with:

- **Node.js**: JavaScript runtime
- **TypeScript**: Type-safe JavaScript
- **Koa**: Lightweight web framework
- **PM2**: Process manager for Node.js applications
- **MySQL**: Database for storing reporting data
- **Redis**: Caching and token storage
- **Google Cloud Platform**:
  - BigQuery for data processing
  - Cloud Storage for file storage

## Architecture Overview

1. **Web Layer**:
   - Nginx as reverse proxy and load balancer
   - Koa for HTTP request handling
   - [server/src/index.ts](mdc:server/src/index.ts) configures the Koa application

2. **API Layer**:
   - Routes defined in [server/src/routers/](mdc:server/src/routers)
   - Controllers in [server/src/controllers/](mdc:server/src/controllers)
   - Input validation with Joi schemas in [server/src/schema/](mdc:server/src/schema)

3. **Business Logic Layer**:
   - Services in [server/src/services/](mdc:server/src/services)
   - Type definitions in [server/src/types/](mdc:server/src/types)

4. **Data Access Layer**:
   - Models in [server/src/models/](mdc:server/src/models)
   - Database utilities in [server/src/db/](mdc:server/src/db)

5. **Infrastructure**:
   - Logging with log4js
   - Session management with koa-session
   - Data compression with koa-compress
   - Environment-specific configuration in [server/src/config/](mdc:server/src/config)

## Key Technical Features

1. **Reporting API**: Generation and retrieval of CSV reports from Google Cloud Storage
2. **Authentication**: Token-based authentication for API access
3. **Error Handling**: Structured error responses and logging
4. **Environment Configuration**: Separate configurations for development, test, and production
