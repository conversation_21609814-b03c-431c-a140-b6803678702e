---
description: Development Workflow
globs: 
alwaysApply: false
---
# Development Workflow

## Development Environment Setup

1. Install dependencies:
```bash
cd server
npm install
```

2. Start development server:
```bash
npm run dev
```
This runs the server in development mode with nodemon for hot reloading.

## Project Scripts

- [server/package.json](mdc:server/package.json): Contains all available npm scripts
- `npm run dev`: Run in development mode with nodemon
- `npm run build`: Build TypeScript files to JavaScript
- `npm run buildprod`: Build for production (includes additional optimization)
- `npm run prod`: Build and start the production server
- `npm run pm2`: Deploy to production using PM2
- `npm run pm2-test`: Deploy to test environment using PM2

## Coding Patterns

1. **Router Structure**:
   - Routes are defined in [server/src/routers/](mdc:server/src/routers)
   - Example: [server/src/routers/reporting-api/report.ts](mdc:server/src/routers/reporting-api/report.ts)

2. **Controller Pattern**:
   - Controllers handle request/response logic
   - Example: [server/src/controllers/reporting-api/report.ts](mdc:server/src/controllers/reporting-api/report.ts)

3. **Service Layer**:
   - Business logic is in service files
   - Example: [server/src/services/reporting-api/report.ts](mdc:server/src/services/reporting-api/report.ts)

4. **Data Access Layer**:
   - Model files handle database operations
   - Example: [server/src/models/reporting-api/report.ts](mdc:server/src/models/reporting-api/report.ts)

## Testing

Test API requests are located in [server/test/request/](mdc:server/test/request) directory.
