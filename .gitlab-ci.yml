variables:
  APP_NAME: saas/rixreportingapi
  AWS_ACCESS_KEY_ID: ${CO_AWS_ACCESS_KEY_ID}
  AWS_SECRET_ACCESS_KEY: ${CO_AWS_SECRET_ACCESS_KEY}
  CONTAINER_REGISTRY: ${CO_USE1_CONTAINER_REGISTRY}
  PROJECT_CONTAINER_REPOSITORY: ${CO_USE1_CONTAINER_REGISTRY}/saas/rix-reporting-api
  CO_EKS_CLUSTER_1_NAME: 'algorix-use1-eks-service'
  SERVICE_NAMESPACE: 'saas-rix-reporting-api'
  GIT_STRATEGY: none


workflow:
  rules:
    - if: $CI_COMMIT_TAG
    - if: $CI_COMMIT_BRANCH

stages:
  - deploy

app-test-deploy:
  stage: deploy
  tags:
    - saas
    - reporting
    - test
    - use
  #rules:
  #  - if: $CI_COMMIT_BRANCH == 'test' && $CI_COMMIT_TAG =~ /^v.*/
  only:
    - test
  script:
    - |
      set -e
      echo $CI_COMMIT_TAG
      echo $CI_COMMIT_BRANCH
      nvm use v18 && \
      node -v && \
      cd /data/htdocs/saas.rix-reporting-api && \
      git checkout test && \
      git fetch --all && \
      git reset --hard origin/test && \
      sh deploy.sh test && \
      exit
  after_script:
    - echo "[test] deploy success"
    #- |
    #    LARK_USER=`curl "http://private-general-lb-9a0cc0deb0ae987b.elb.us-east-1.amazonaws.com:1119/lark/batch_get_lark_id?emails=${GITLAB_USER_EMAIL}&ts=1622778676&rs=H3YRg5dxBrNJ&sign=a4dfdf9d7da37d93de9c0a2510fe227e" | sed -n 's/.*"user_id":"\([^"]*\)".*/\1/p'`
    - |
      curl -X POST 'https://open.feishu.cn/open-apis/bot/v2/hook/2842f829-b85d-4a43-9964-dc99c82f5dcc' -d "{\"msg_type\":\"post\",\"content\":{\"post\":{\"zh_cn\":{\"title\":\"[Notice] CI/CD Info [test] ${GITLAB_USER_EMAIL}\",\"content\":[[{\"tag\":\"text\",\"text\":\"${CI_PROJECT_PATH} deploy ${CI_JOB_STATUS}\"}]]}}}}" -H 'Content-Type: application/json'

app-prod-deploy:
  stage: deploy
  tags:
    - saas
    - reporting
    - prod
    - use
  #rules:
  #  - if: $CI_COMMIT_BRANCH == 'test' && $CI_COMMIT_TAG =~ /^v.*/
  only:
    - /^v.*/
  script:
    - |
      set -e
      echo $CI_COMMIT_TAG
      echo $CI_COMMIT_BRANCH
      nvm use v18 && \
      node -v && \
      cd /data/htdocs/saas.rix-reporting-api && \
      git checkout master && \
      git fetch --all && \
      git reset --hard origin/master && \
      sh deploy.sh prod && \
      exit
  after_script:
    - echo "[prod] deploy success"
    - |
      curl -X POST 'https://open.feishu.cn/open-apis/bot/v2/hook/2842f829-b85d-4a43-9964-dc99c82f5dcc' -d "{\"msg_type\":\"post\",\"content\":{\"post\":{\"zh_cn\":{\"title\":\"[Notice] CI/CD Info [new-prod] ${GITLAB_USER_EMAIL}\",\"content\":[[{\"tag\":\"text\",\"text\":\"${CI_PROJECT_PATH} deploy ${CI_JOB_STATUS}\"}]]}}}}" -H 'Content-Type: application/json'

