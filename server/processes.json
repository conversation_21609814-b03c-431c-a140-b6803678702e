{"apps": [{"name": "saas.rix-reporting-api", "script": "./dist/index.js", "log_date_format": "YYYY-MM-DD HH:mm:ss", "error_file": "./logs/node-app.stderr.log", "out_file": "./logs/node-app.stdout.log", "pid_file": "./pids/node-geo-api.pid", "min_uptime": "200s", "max_restarts": 10, "max_memory_restart": "500M", "merge_logs": false, "exec_interpreter": "node", "exec_mode": "cluster", "instances": "max", "autorestart": true, "vizion": false, "env": {"NODE_ENV": "prod", "PORT": 4090}}, {"name": "test.saas.rix-reporting-api", "script": "./dist/index.js", "log_date_format": "YYYY-MM-DD HH:mm:ss", "error_file": "./logs/node-app.stderr.log", "out_file": "./logs/node-app.stdout.log", "pid_file": "./pids/node-geo-api.pid", "min_uptime": "200s", "max_restarts": 10, "max_memory_restart": "500M", "merge_logs": false, "exec_interpreter": "node", "exec_mode": "cluster", "instances": "max", "autorestart": true, "vizion": false, "env": {"NODE_ENV": "test", "PORT": 4091}}]}