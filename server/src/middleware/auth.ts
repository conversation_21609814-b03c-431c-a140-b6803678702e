/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2023-07-24 18:01:08
 * @LastEditors: chen<PERSON>dan
 * @LastEditTime: 2024-01-11 10:18:27
 * @Description:
 */

import { Context, Next } from 'koa';
import { getConfig, md5 } from '@/utils/common';
import { commonModel } from '@/models';
import {
  setRedisSetCountByValue,
  existRedisKey,
  getRedisSetCountByValue
} from '@/db/redis';
import { appLogger } from '@/config/log4js';

const { redisConfig } = getConfig();
const { tnt_host_prefix_key } = redisConfig;

export async function resolveHostPrefix(ctx: Context, next: Next) {
  const env = process.env.NODE_ENV || '';
  // skip if run in development
  if (!['prod', 'test'].includes(env)) {
    ctx.state.host_prefix = `allowed`;
  } else {
    const host = (ctx.request.host && ctx.request.host.trim()) || '';
    // not private domain
    if (host.endsWith('rixengine.com')) {
      // eslint-disable-next-line prefer-destructuring
      ctx.state.host_prefix = host.split('.')[0];
    } else {
      const domain = host.replace('rpt.', '');
      const res = await commonModel.getHostPrefixFromPvDomain(domain);
      ctx.state.host_prefix = res?.[0]?.host_prefix || '';
    }
  }
  appLogger.info(
    `visit url=[${ctx.originalUrl}], host_prefix=[${ctx.state.host_prefix}]`
  );
  return await next();
}

/**
 * 注意：未被使用
 * 功能：验证域名是否合法 本地不管
 */
export async function validHostPrefix(ctx: Context, next: Next) {
  //  测试环境去掉前缀验证
  if (['prod'].includes(process.env.NODE_ENV as string)) {
    const host_key = md5(`${redisConfig.session_key}_${tnt_host_prefix_key}`);
    const exists = await existRedisKey(host_key);
    const host_prefix = (ctx.state.host_prefix as string) || '';
    // 域名默认不存在
    let flag = false;
    if (!exists) {
      const data = await commonModel.getAllTenantHostPrefix();
      const allHostPrefix = data?.map(item => item.host_prefix) || [];
      // 保存所有的域名前缀
      await setRedisSetCountByValue(host_key, allHostPrefix || []);
      const count = await getRedisSetCountByValue(host_key, ctx.request.host);
      flag = allHostPrefix.includes(host_prefix) || count === 1;
    } else {
      // 判断是私有域名还是公用域名
      const count = await getRedisSetCountByValue(host_key, host_prefix);
      flag = count === 1;
    }
    if (!flag) {
      ctx.status = 404;
      ctx.body = 'Not Found';
    } else {
      return await next();
    }
  } else {
    return await next();
  }
}
