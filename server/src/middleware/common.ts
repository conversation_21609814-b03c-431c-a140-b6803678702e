/*
 * @Author: ch<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-03-15 19:28:04
 * @LastEditors: chen<PERSON><PERSON>
 * @LastEditTime: 2024-01-12 15:19:08
 * @Description:
 */
import { errorLogger } from '@/config/log4js';
import { handleAlertToFeiShu } from '@/utils/alert';
import { getRequestParams } from '@/utils/http';
import { getCtxResult } from '@/utils/validation';
import { Context, Next } from 'koa';

// 获取请求参数信息
export function getTokenAndSidUrl(ctx: Context) {
  let sid: number = 0;
  let token: string = '';
  const url: string = ctx.request.originalUrl.split('?')[0] || '';
  if (ctx.request.method === 'POST') {
    sid = Number(ctx.request.header['x-userid']) || 0;
    token = (ctx.request.header['x-authorization'] as string) || '';
  } else {
    const { query } = ctx.request;
    sid = Number(query['x-userid']) || 0;
    token = (query['x-authorization'] as string) || '';
  }
  return { sid, token, api_url: url };
}

// 全部错误捕获
export async function resolveError(ctx: Context, next: Next) {
  try {
    return await next();
  } catch (error: any) {
    console.log('xx捕获错误', error);
    const { sid, token } = getTokenAndSidUrl(ctx);
    const params = getRequestParams(ctx);
    const msg = `
      url=[${ctx.request.originalUrl}],
      origin=[${ctx.request.origin}],
      method=[${ctx.request.method}],
      params=[${JSON.stringify(params)}],
      errorMsg=[${error.message}],
      error=[${error}],
      sid=[${sid}],
      token=[${token || ''}],
      host_prefix=[${ctx.state.host_prefix || ''}],
    `;
    handleAlertToFeiShu(
      `Saas Std API Catch Error, env=[${process.env.NODE_ENV}]`,
      msg
    );
    errorLogger.error(
      `sid="${sid}",token=${token}, resolveError catch error=[${
        error.message
      }], params=[${JSON.stringify(params)}]`
    );
    const result = getCtxResult('ERROR_SYS', null);
    result.status.message =
      process.env.NODE_ENV === 'prod' ? result.status.message : error.message;
    ctx.body = result;
  }
}
