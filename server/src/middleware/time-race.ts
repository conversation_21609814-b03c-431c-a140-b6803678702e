import { errorLogger } from '@/config/log4js';
import { getCtxResult } from '@/utils/validation';
import { Context, Next } from 'koa';
import { getTokenAndSidUrl } from './common';

// 设置接口超时时间
const setInterfaceTimeOut = async function (
  time: number,
  ctx: Context,
  next: Next
) {
  const { sid, token } = getTokenAndSidUrl(ctx);
  const msg = `
    url=[${ctx.request.originalUrl}],
    origin=[${ctx.request.origin}],
    method=[${ctx.request.method}],
    sid=[${sid}],
    token=[${token || ''}],
    host_prefix=[${ctx.state.host_prefix || ''}],
  `;
  return Promise.race([
    new Promise((resolve, reject) => {
      const timer = setTimeout(() => {
        clearTimeout(timer);
        errorLogger.error(
          `${msg} ${time} ${new Date().toLocaleString()} Request timeout`
        );
        const result = getCtxResult('REQUEST_TIMEOUT', []);
        ctx.body = result;
        resolve(1);
      }, time);
      // eslint-disable-next-line no-async-promise-executor
    }),
    new Promise(async (resolve, reject) => {
      try {
        await next();
        resolve(1);
      } catch (error: any) {
        errorLogger.error(
          `${msg} ${time} ${new Date().toLocaleString()} error=[${
            error.message
          }]}`
        );
        reject(error);
      }
    })
  ]);
};

// 设置接口超时时间
export const setApiTimeOut = function (time: number) {
  return async function (ctx: Context, next: Next) {
    ctx.request.socket.setTimeout(time);
    return setInterfaceTimeOut(time, ctx, next);
  };
};
