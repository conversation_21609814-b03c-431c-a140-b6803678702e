/*
 * @Author: ch<PERSON><PERSON><PERSON>
 * @Date: 2024-01-12 15:16:44
 * @LastEditors: chenmudan
 * @LastEditTime: 2024-02-05 11:53:45
 * @Description:
 */

import { Code } from '@/codes';
import { errorLogger } from '@/config/log4js';
import { TimeZoneMap } from '@/constants/timezone';
import { getRedisByKey } from '@/db/redis';
import { getUserInfo } from '@/middleware/helper/get-user-info';
import { reportService } from '@/services';
import { handleAlertToFeiShu } from '@/utils/alert';
import { getConfig, md5 } from '@/utils/common';
import { decrypt } from '@/utils/config';
import { getRequestParams } from '@/utils/http';
import { getCtxResult } from '@/utils/validation';
import Joi from 'joi';
import { Context, Next } from 'koa';
import moment from 'moment';
import { getTokenAndSidUrl } from './common';
import {
  handleInvalidResponse,
  handleInvalidStatus,
  handleInvalidToken
} from './helper/invalid-response-handle';

const { redisConfig } = getConfig();

/**
 * 验证用户是否合法
 * @description 异步中间件函数，用于验证用户的身份和状态。检查 token 和 sid 的有效性，
 * 查询用户信息并验证用户状态，同时处理时区信息并设置到 ctx.state 中。
 * 如果验证失败，会调用相应的错误处理函数。
 * @param {Context} ctx - Koa 上下文对象，包含请求和响应信息
 * @param {Next} next - 下一个中间件函数
 * @returns {Promise<void>} 无返回值，但会修改 ctx.state 并可能调用 next() 或返回错误响应
 * @sideEffects 修改 ctx.state，设置 user_info、bg_time_zone 和 timezone 属性
 * @throws 如果用户验证失败，会通过 handleInvalidToken 或 handleInvalidStatus 返回错误响应
 */
export async function validUser(ctx: Context, next: Next) {
  const { sid, token, api_url } = getTokenAndSidUrl(ctx);
  // sid 必须是数字 不能是string 否则会报错
  // const num = sid ? sid.replace(/[^0-9]/g, '') : '';
  // 检查 token 和 sid 的有效性
  if (!token || !sid) {
    return handleInvalidToken(ctx, sid, token, api_url);
  }

  // 根据 api_url 查询用户信息
  const userInfo = await getUserInfo(
    api_url,
    sid,
    token,
    ctx.state.host_prefix
  );

  if (!userInfo || !userInfo.length) {
    return handleInvalidToken(ctx, sid, token, api_url);
  }

  // 这里需要判断用户 api_status 是否为 1，如果不是则返回错误信息
  if (userInfo.some(item => item.api_status !== 1)) {
    return handleInvalidStatus(ctx, sid, token, api_url);
  }

  // 处理请求参数
  const params = getRequestParams(ctx);
  const timezone = (params.timezone || 'UTC+0').replace(' ', '+');

  ctx.state = {
    user_info: userInfo[0],
    // 将时区信息映射到正确的时区
    bg_time_zone: TimeZoneMap[timezone] || 'Etc/UTC',
    timezone: timezone
  };

  return await next();
}

/**
 * 验证每日访问次数限制
 * @description 异步中间件函数，用于检查用户每日 API 访问次数是否超过限制（50次）。
 * 使用 Redis 存储每日访问计数，当达到限制时返回错误响应并发送飞书告警。
 * 只有成功的请求才会被计数，失败的请求不计入限制。
 * @param {Context} ctx - Koa 上下文对象，包含请求和响应信息
 * @param {Next} next - 下一个中间件函数
 * @returns {Promise<void>} 无返回值，但会修改 ctx.state 并可能返回错误响应
 * @sideEffects 修改 ctx.state，设置 redis_count_key、redis_count 和 redis_expired 属性；
 * 当达到限制时发送飞书告警
 * @throws 当访问次数超过限制时，返回 REQUEST_COUNT_LIMIT 错误响应
 */
export async function validVisitDayLimit(ctx: Context, next: Next) {
  const { sid, token, api_url } = getTokenAndSidUrl(ctx);
  const day = moment().format('YYYYMMDD');
  // 每天的key 仅api使用 origin 增加api_url 是为了预防 demand/supply sid/token相同导致
  const key = md5(
    `${ctx.state.host_prefix || ''}_${api_url}_${sid}_${token}_${day}`
  );
  // 当天查询次数
  const count = Number((await getRedisByKey(key)) || 0);
  if (count >= redisConfig.API_DAY_MAX_LIMIT) {
    ctx.response.status = 200;
    ctx.body = getCtxResult('REQUEST_COUNT_LIMIT', []);
    const params = getRequestParams(ctx);
    const msg = `
      url=[${ctx.request.originalUrl}],
      api_url=[${api_url}]
      origin=[${ctx.request.origin}],
      method=[${ctx.request.method}],
      sid=[${sid}],
      token=[${token || ''}],
      params=[${JSON.stringify(params)}]
      host_prefix=[${ctx.state.host_prefix || ''}],
      redis_key=[${key}]
    `;
    errorLogger.error(`[Reporting API] msg=${msg} request rate limit failed.`);
    // 发送飞书告警, 达到限制时只报警一次, redisConfig.API_DAY_MAX_LIMIT 为 50，告警取 100
    if (count === 100) {
      handleAlertToFeiShu(
        `Saas Std API Access Limit ${count}, env=[${process.env.NODE_ENV}]`,
        msg
      );
    }
  } else {
    // 一天过期
    const expired = 24 * 60 * 60;
    const num = count + 1;
    // await setRedisByKey(key, num, expired);
    // 只统计成功的次数 失败的不管
    ctx.state.redis_count_key = key;
    ctx.state.redis_count = num;
    ctx.state.redis_expired = expired;
    return await next();
  }
}

/**
 * 验证 sid 和 token 的数据类型
 * @description 同步中间件函数，使用 Joi 验证 sid 和 token 的数据类型是否正确。
 * sid 必须是数字类型，token 必须是字符串类型。如果验证失败，返回参数无效错误。
 * @param {Context} ctx - Koa 上下文对象，包含请求和响应信息
 * @param {Next} next - 下一个中间件函数
 * @returns {void} 无返回值，验证通过时调用 next()，失败时返回错误响应
 * @throws 当 sid 或 token 类型不正确时，通过 handleInvalidResponse 返回 PARAMS_INVALID 错误
 */
export function validateSidAndTokenType(ctx: Context, next: Next) {
  const { sid, token } = getTokenAndSidUrl(ctx);
  const { error } = Joi.object({
    sid: Joi.number().required().messages({
      'number.base': 'x-userid must be a number',
      'any.required': 'x-userid is required'
    }),
    token: Joi.string().required().messages({
      'string.base': 'x-authorization must be a string',
      'any.required': 'x-authorization is required'
    })
  }).validate({ sid, token });
  if (error) {
    return handleInvalidResponse(
      ctx,
      sid,
      token,
      ctx.request.originalUrl,
      Code.PARAMS_INVALID,
      error.message
    );
  }
  return next();
}

/**
 * 验证 token 的数据类型
 * @description 同步中间件函数，使用 Joi 验证 token 的数据类型是否为字符串。
 * 如果验证失败，返回参数无效错误。与 validateSidAndTokenType 的区别是只验证 token。
 * @param {Context} ctx - Koa 上下文对象，包含请求和响应信息
 * @param {Next} next - 下一个中间件函数
 * @returns {void} 无返回值，验证通过时调用 next()，失败时返回错误响应
 * @throws 当 token 类型不正确时，通过 handleInvalidResponse 返回 PARAMS_INVALID 错误
 */
export function validateTokenType(ctx: Context, next: Next) {
  const { token } = getTokenAndSidUrl(ctx);

  const { error } = Joi.object({
    token: Joi.string().required().messages({
      'string.base': 'x-authorization must be a string',
      'any.required': 'x-authorization is required'
    })
  }).validate({ token });
  if (error) {
    return handleInvalidResponse(
      ctx,
      0,
      token,
      ctx.request.originalUrl,
      Code.PARAMS_INVALID,
      error.message
    );
  }
  return next();
}

/**
 * 验证并获取租户 ID
 * @description 异步中间件函数，通过 host_prefix 获取租户信息并设置 tnt_id 到 ctx.state 中。
 * 如果无法获取到有效的租户信息，返回 403 错误响应。
 * @param {Context} ctx - Koa 上下文对象，包含请求和响应信息
 * @param {Next} next - 下一个中间件函数
 * @returns {Promise<void>} 无返回值，但会修改 ctx.state 并可能返回错误响应
 * @sideEffects 修改 ctx.state，设置 tnt_id 属性
 * @throws 当无法获取租户信息时，返回 UNSUPPORTED_TNT 错误响应
 */
export async function validTntId(ctx: Context, next: Next) {
  const { host_prefix = '' } = ctx.state;
  // 通过 host_prefix 获取 tnt_id，设置到 ctx.state.tnt_id
  const data = await reportService.getTntInfo(host_prefix);
  if (Array.isArray(data) && data.length) {
    ctx.state.tnt_id = data[0].tnt_id || 0;
    return await next();
  }
  ctx.response.status = 403;
  ctx.body = getCtxResult('UNSUPPORTED_TNT', '');
  return;
}

/**
 * 通过租户 ID 和 token 获取用户信息
 * @description 异步工具函数，根据租户 ID 和 token 查询用户信息。
 * 如果参数无效（tnt_id 或 token 为空），则返回 null。
 * @param {number} tnt_id - 租户 ID，必须为有效数字
 * @param {string} token - 用户认证 token，必须为有效字符串
 * @returns {Promise<Array|null>} 返回用户信息数组，如果查询失败或参数无效则返回 null
 * @throws 可能抛出数据库查询相关的异常
 */
export async function getUserInfoByTntId(tnt_id: number, token: string) {
  if (!tnt_id || !token) {
    return null;
  }
  return reportService.getUserInfo(tnt_id, token);
}

/**
 * 验证报告下载权限
 * @description 异步中间件函数，验证用户是否有权限下载报告。
 * 仅当前租户的超管（type=1）和管理员（type=4）才可以获取下载链接。
 * 通过 tnt_id 和 token 获取用户信息，验证用户角色权限。
 * @param {Context} ctx - Koa 上下文对象，包含请求和响应信息
 * @param {Next} next - 下一个中间件函数
 * @returns {Promise<void>} 无返回值，验证通过时调用 next()，失败时返回错误响应
 * @sideEffects 从 ctx.state 读取 tnt_id，从请求头读取 x-authorization token
 * @throws 当用户权限不足时，返回 TOKEN_INVALID 错误响应
 */
export async function validReportTokenPermission(ctx: Context, next: Next) {
  const { tnt_id = 0 } = ctx.state;
  const token: any = ctx.request.header['x-authorization'];

  // 1. 通过 tnt_id 和 token 获取到用户信息
  const [userInfo] = (await getUserInfoByTntId(tnt_id, token)) || [];

  // 2. 通过用户信息和 type 验证。1: 租户超管, 4: 管理员
  if (userInfo && [1, 4].includes(userInfo.type)) {
    return await next();
  }

  ctx.response.status = 403;
  ctx.body = getCtxResult('TOKEN_INVALID', '');
}

/**
 * 验证报告文件下载权限
 * @description 异步中间件函数，验证用户是否有权限下载指定的报告文件。
 * 从 URL 参数中获取加密的文件名，解密后提取原始 token，验证用户身份。
 * 仅当前租户的用户才可以下载对应的报告文件。
 * @param {Context} ctx - Koa 上下文对象，包含请求和响应信息
 * @param {Next} next - 下一个中间件函数
 * @returns {Promise<void>} 无返回值，验证通过时调用 next()，失败时返回错误响应
 * @sideEffects 从 ctx.state 读取 tnt_id，从 ctx.params 读取文件名参数，解密 token
 * @throws 当文件名参数缺失时，返回 PARAMS_INVALID 错误响应；
 * 当用户身份验证失败时，返回 TOKEN_INVALID 错误响应
 */
export async function validDownloadReportToken(ctx: Context, next: Next) {
  const { tnt_id = 0 } = ctx.state;
  const { name = '' } = ctx.params;

  if (!name) {
    ctx.response.status = 400;
    ctx.body = getCtxResult('PARAMS_INVALID', 'File name is required');
    return;
  }

  // 解密文件名参数获取真实token
  const [encryptedToken] = name.split('.');
  const decryptedToken = decrypt(encryptedToken);
  const [originToken = ''] = decryptedToken.split('_');

  const [userInfo] = (await getUserInfoByTntId(tnt_id, originToken)) || [];

  if (userInfo) {
    return await next();
  }

  ctx.response.status = 403;
  ctx.body = getCtxResult('TOKEN_INVALID', '');
}
