import { Code, Message } from '@/codes';
import { errorLogger } from '@/config/log4js';
import { Context } from 'koa';
import moment from 'moment';

const RESULT = {
  status: {
    code: Code.ERROR_SYS,
    msg: Message.ERROR_SYS
  },
  timestamp: moment.utc(new Date()).format('ddd MMM D HH:mm:ss Z YYYY'),
  data: []
};

/**
 * 处理非法请求的响应结果
 * @param ctx
 * @param sid
 * @param token
 * @param api_url
 * @param code
 * @param msg
 */
export function handleInvalidResponse(
  ctx: Context,
  sid: number,
  token: string,
  api_url: string,
  code: number,
  msg: string
) {
  const result = { ...RESULT };
  ctx.response.status = 200;
  result.status = {
    code: code,
    msg: msg
  };
  result.data = [];
  errorLogger.error(
    `[Reporting API] sid=[${sid}],token=[${token}],api_url=[${api_url}] ${msg}`
  );
  ctx.body = result;
}

export function handleInvalidToken(
  ctx: Context,
  sid: number,
  token: string,
  api_url: string
) {
  handleInvalidResponse(
    ctx,
    sid,
    token,
    api_url,
    Code.TOKEN_INVALID,
    Message.TOKEN_INVALID
  );
}

export function handleInvalidStatus(
  ctx: Context,
  sid: number,
  token: string,
  api_url: string
) {
  handleInvalidResponse(
    ctx,
    sid,
    token,
    api_url,
    Code.USER_STATUS_INVALID,
    Message.USER_STATUS_INVALID
  );
}
