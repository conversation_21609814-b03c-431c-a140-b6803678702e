import { commonModel } from "@/models";

interface ApiHandler {
  urls: string[];
  handler: (
    sid: number,
    token: string,
    hostPrefix: string
  ) => Promise<
    {
      status: number;
      api_status: number;
      token: string;
      tnt_id: number;
      seller_id?: number;
      buyer_id?: number;
      partner_id?: number;
      dp_id?: number;
      sp_id?: number;
      integration_type?: number;
    }[]
  >;
}

/**
 * @description: 不同 api 对应不同的 获取用户信息的方法
 */
const apiHandlers: Record<string, ApiHandler> = {
  supply: {
    urls: ['/ssp/api/v1', '/ssp/api/v2'],
    handler: commonModel.getSupplyBySidToken
  },
  demand: {
    urls: ['/demand/api/v1', '/demand/api/v2'],
    handler: commonModel.getDemandBySidToken
  },
  demandPartner: {
    urls: ['/partner/api/demand'],
    handler: commonModel.getDemandPartnerBySidToken
  },
  supplyPartner: {
    urls: ['/partner/api/supply'],
    handler: commonModel.getSupplyPartnerBySidToken
  }
};

/**
 * 根据 api_url 获取用户信息的辅助函数
 * @param api_url string
 * @param sid number
 * @param token string
 * @param hostPrefix string
 * @returns
 */
export async function getUserInfo(
  api_url: string,
  sid: number,
  token: string,
  hostPrefix: string
) {
  for (const [key, { urls, handler }] of Object.entries(apiHandlers)) {
    if (urls.includes(api_url)) {
      return await handler(sid, token, hostPrefix);
    }
  }
  return null; // 如果 api_url 不匹配任何已知值，返回 null
}
