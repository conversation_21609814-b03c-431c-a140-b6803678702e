/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2023-07-12 11:43:58
 * @LastEditors: chen<PERSON><PERSON>
 * @LastEditTime: 2024-02-06 11:27:16
 * @Description:
 */

import Joi from 'joi';
import moment from 'moment';

const dateReg = /^\d{4}-\d{2}-\d{2}$/;

const validDate = (val: string) => {
  const date = new Date(val);
  const nYear = date.getFullYear();
  const nMonth = date.getMonth() + 1;
  const nDate = date.getDate();
  const arr = val.split('-');
  const year = +arr[0];
  const month = +arr[1];
  const day = +arr[2];
  return year === nYear && nMonth === month && day === nDate;
};

// 验证day 参数 v1版本使用
export const validReportDay = (value: any, helper: Jo<PERSON>.CustomHelpers<any>, limit = 32, dateFormate = 'YYYY-MM-DD') => {
  const { day } = value;
  const message: any = 'invalid date';
  const regInvalid = !dateReg.test(day);
  const dateInvalid = Number.isNaN(Date.parse(`${day}`));
  const cur = moment();
  const flag = validDate(day);
  // 格式不正确
  if (regInvalid || dateInvalid || !flag) {
    return helper.message(message);
  }
  // 默认只支持查询32天内的数据
  if (cur.diff(moment(day, dateFormate), 'days') > limit) {
    const m: any = `The day must last ${limit} day to last day.`;
    return helper.message(m);
  }
  return true;
};

// limit 是查询区间限定多少天内 maxRage是最远支持查询天数 默认只支持查询60天内的数据 不支持查询当天的数据
export const validReportDateParams = (value: any, helper: Joi.CustomHelpers<any>, limit: number, maxRange = 60, dateFormate = 'YYYY-MM-DD') => {
  const { start_date, end_date } = value;
  const message: any = 'invalid date';
  const regInvalid = !dateReg.test(start_date) || !dateReg.test(end_date);
  const dateInvalid = Number.isNaN(Date.parse(`${start_date}`)) || Number.isNaN(Date.parse(`${end_date}`));
  const cur = moment();
  const flag = validDate(start_date) && validDate(end_date);
  // 格式不正确
  if (regInvalid || dateInvalid || !flag) {
    return helper.message(message);
  }
  const start = moment(start_date, dateFormate).format('YYYYMMDD');
  const end = moment(end_date, dateFormate).format('YYYYMMDD');
  // 日期范围不正确
  if ((+start) > (+end)) {
    const t: any = 'The end_date must larger or equal then start_date.';
    return helper.message(t);
  }
  // 超出查询范围
  if (moment(end_date, dateFormate).diff(moment(start_date, dateFormate), 'days') > limit) {
    const m: any = `The date interval needs to be within ${limit} days.`;
    return helper.message(m);
  }
  // 默认只支持查询32天内的数据
  if (cur.diff(moment(start_date, dateFormate), 'days') > maxRange) {
    const m: any = `The date range must last ${maxRange} day to current.`;
    return helper.message(m);
  }
  return value;
};

export const validSupplyDimension = (value: any, helper: Joi.CustomHelpers<any>) => {
  const { integration_type, dimensions = [] } = value;
  if ([1, 2].includes(integration_type)) {
    const flag = dimensions.some((v: string) => !['day', 'region'].includes(v));
    if (flag) {
      const m: any = `The dimensions only support day, region`;
      return helper.message(m);
    }
  }
  // // 1060 客户暂停
  // const f1 = seller_id === 36281 && tnt_id === 1060;
  // if (f1) {
  //   const flag = dimensions.some((v: string) => v === 'placement_id');
  //   if (flag) {
  //     const m: any = `The dimensions only support day,app_bundle_id,country,ad_format`;
  //     return helper.message(m);
  //   }
  // } else if ([1, 2].includes(integration_type)) {
  //   const flag = dimensions.some((v: string) => v !== 'day');
  //   if (flag) {
  //     const m: any = `The dimensions only support day`;
  //     return helper.message(m);
  //   }
  // } else {
  //   const f2 = dimensions.some((v: string) => ['country', 'ad_format'].includes(v));
  //   if (f2) {
  //     const m: any = `The dimensions only support day,app_bundle_id,placement_id`;
  //     return helper.message(m);
  //   }
  // }
  return true;
};
