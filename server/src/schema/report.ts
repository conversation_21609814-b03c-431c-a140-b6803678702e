/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-07-07 17:54:53
 * @LastEditors: ch<PERSON><PERSON><PERSON>
 * @LastEditTime: 2024-01-15 11:17:17
 * @Description:
 */
import { AllTimeZone } from '@/constants/timezone';
import Joi from 'joi';
import {
  validReportDateParams,
  validReportDay,
  validSupplyDimension
} from './utils';

// 定义用户认证字段
const UserAuthFields = {
  'x-userid': Joi.number().optional(),
  'x-authorization': Joi.string().optional()
};

export const ReportV1Scheme = Joi.object({
  day: Joi.string().required(),
  ...UserAuthFields
}).custom((value, helper) => validReportDay(value, helper, 32));

// 第二个版本的schema
export const DemandReportV2Scheme = Joi.object({
  start_date: Joi.string().required(),
  end_date: Joi.string().required(),
  timezone: Joi.string().valid(...AllTimeZone),
  dimensions: Joi.array().items(Joi.string().valid('day', 'region')),
  ...UserAuthFields
}).custom((value, helper) => validReportDateParams(value, helper, 15));

// 第二个版本的schema
export const SupplyReportV2Scheme = Joi.object({
  start_date: Joi.string().required(),
  end_date: Joi.string().required(),
  dimensions: Joi.array().items(
    Joi.string().valid(
      'day',
      // 'app_bundle_id',
      // 'placement_id',
      // 'country',
      // 'ad_format',
      'region'
    )
  ),
  timezone: Joi.string().valid(...AllTimeZone),
  ...UserAuthFields
})
  .custom((value, helper) => validReportDateParams(value, helper, 15))
  .custom((value, help) => validSupplyDimension(value, help));

// 下载报表接口
export const DownloadReportSchema = Joi.object({
  date: Joi.array()
    .items(Joi.string())
    .required()
    .custom((value, helper) => {
      // 验证正确的日期 以及日期格式是否符合yyyy-MM-dd
      const m: any = 'Please input valid date string';
      const reg = /\d{4}-\d{2}-\d{2}/;
      const flag = value.some(
        (v: string) => !reg.test(v) || Number.isNaN(Date.parse(v))
      );
      if (flag) {
        return helper.message(m);
      }
      return true;
    }),
  ...UserAuthFields
});

// partner 下游参数
export const SupplyPartnerReportScheme = Joi.object({
  start_date: Joi.string().required(),
  end_date: Joi.string().required(),
  dimensions: Joi.array().items(Joi.string().valid('day', 'region')),
  timezone: Joi.string().valid(...AllTimeZone),
  ...UserAuthFields
}).custom((value, helper) => validReportDateParams(value, helper, 15));

// partner 上游参数
// export const DemandPartnerReportScheme = Joi.object({
//   start_date: Joi.string().required(),
//   end_date: Joi.string().required(),
//   dimensions: Joi.array().items(
//     Joi.string().valid(
//       'day',
//       'region'
//     )
//   )
// }).custom((value, helper) => validReportDateParams(value, helper, 15));
