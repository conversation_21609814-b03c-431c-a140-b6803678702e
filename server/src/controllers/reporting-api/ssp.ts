/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2023-07-24 16:05:10
 * @LastEditors: chen<PERSON>dan
 * @LastEditTime: 2023-12-08 11:09:05
 * @Description:
 */

import { setRedisByKey } from '@/db/redis';
import { ReportV1Scheme, SupplyReportV2Scheme } from '@/schema/report';
import { sspServiceV2 } from '@/services';
import { getCtxResult } from '@/utils/validation/response';
import { transformV1ToV2Params } from '@/utils/transform';
import { validateParams } from '@/utils/validation/params';
import { Context } from 'koa';

class SSPApiCtrl {
  @validateParams(SupplyReportV2Scheme)
  async getSSPReportListV2(ctx: Context) {
    const api_url = ctx.originalUrl.split('?')[0];
    const formData = ctx.request.body;
    const list = await sspServiceV2.getSupplyReportV2(formData, {
      tag: api_url,
      tenantId: formData.tnt_id
    });
    const { redis_count_key, redis_count, redis_expired } = ctx.state;
    if (list) {
      await setRedisByKey(redis_count_key, redis_count, redis_expired);
      ctx.body = getCtxResult('SUCCESS', list);
      return;
    }
    ctx.body = getCtxResult('ERROR_SYS');
  }

  @validateParams(ReportV1Scheme)
  async getSSPReportListV1(ctx: Context) {
    const api_url = ctx.originalUrl.split('?')[0];
    const formData = ctx.request.body;
    // 转换格式，以匹配 v2 的的入参
    const params = transformV1ToV2Params(formData);
    const list = await sspServiceV2.getSupplyReportV2(params, {
      tag: api_url,
      tenantId: formData.tnt_id
    });
    const { redis_count_key, redis_count, redis_expired } = ctx.state;
    if (list) {
      await setRedisByKey(redis_count_key, redis_count, redis_expired);
      ctx.body = getCtxResult('SUCCESS', list);
      return;
    }
    ctx.body = getCtxResult('ERROR_SYS');
  }
}

export const sspReportApiController = new SSPApiCtrl();
