/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2023-07-24 16:05:10
 * @LastEditors: chen<PERSON>dan
 * @LastEditTime: 2023-12-08 11:23:22
 * @Description:
 */

import { setRedisByKey } from '@/db/redis';
import { DemandReportV2Scheme, ReportV1Scheme } from '@/schema/report';
import { demandServiceV2 } from '@/services';
import { getCtxResult } from '@/utils/validation/response';
import { transformV1ToV2Params } from '@/utils/transform';
import { validateParams } from '@/utils/validation/params';
import { Context } from 'koa';

class DemandApiCtrl {
  @validateParams(DemandReportV2Scheme)
  async getDemandReportListV2(ctx: Context) {
    const api_url = ctx.originalUrl.split('?')[0];
    const formData = ctx.request.body;

    const list = await demandServiceV2.getDemandReportV2(formData, {
      tag: api_url,
      tenantId: formData.tnt_id
    });
    const { redis_count_key, redis_count, redis_expired } = ctx.state;
    if (list) {
      await setRedisByKey(redis_count_key, redis_count, redis_expired);
      ctx.body = getCtxResult('SUCCESS', list);
      return;
    }
    ctx.body = getCtxResult('ERROR_SYS');
  }

  @validateParams(ReportV1Scheme)
  async getDemandReportListV1(ctx: Context) {
    const api_url = ctx.originalUrl.split('?')[0];
    const formData = ctx.request.body;
    // 转换格式，以匹配 v2 的的入参
    const params = transformV1ToV2Params(formData);
    const list = await demandServiceV2.getDemandReportV2(params, {
      tag: api_url,
      tenantId: formData.tnt_id
    });
    const { redis_count_key, redis_count, redis_expired } = ctx.state;
    if (list) {
      await setRedisByKey(redis_count_key, redis_count, redis_expired);
      ctx.body = getCtxResult('SUCCESS', list);
      return;
    }
    ctx.body = getCtxResult('ERROR_SYS');
  }
}

export const demandReportApiController = new DemandApiCtrl();
