/*
 * @Author: chen<PERSON><PERSON>
 * @Date: 2024-01-15 11:16:08
 * @LastEditors: chenmudan
 * @LastEditTime: 2024-01-15 15:19:24
 * @Description:
 */

import { appLogger, errorLogger } from '@/config/log4js';
import { getRedisByKey } from '@/db/redis';
import { DownloadReportSchema } from '@/schema/report';
import { reportService } from '@/services';
import { downloadGCPStreamFile } from '@/utils/database';
import { validateParams } from '@/utils/validation/params';
import { getCtxResult } from '@/utils/validation/response';
import { Context } from 'koa';

class ReportCtrl {
  // 获取下载的链接
  @validateParams(DownloadReportSchema)
  async getDownloadReportUrlsV1(ctx: Context) {
    const params = ctx.request.body;
    params.tnt_id = ctx.state.tnt_id || 0;
    const userToken: any = ctx.request.header['x-authorization'] || '';
    const data = await reportService.getDownloadReportUrlsV1(
      params,
      ctx.origin,
      userToken
    );
    if (data) {
      appLogger.info(
        `getDownloadReportUrlsV1 result=[${JSON.stringify(data)}]`
      );
      ctx.body = getCtxResult('SUCCESS', data);
      return;
    }
    ctx.body = getCtxResult('ERROR_SYS');
  }

  async getReportCsv(ctx: Context) {
    const result: any = 'Not Found';
    const { name } = ctx.params;
    const [token = '', fileSuffix = 'csv'] = name.split('.') || [];
    // 验证文件名称是否正确
    const isCorrect = fileSuffix.toLowerCase() === 'csv';
    appLogger.info(`fileVisit name=[${name}]`);
    if (isCorrect) {
      const file_path: any = await getRedisByKey(token);
      appLogger.info(`pdfVisit params=[${file_path}]`);
      console.log('xx路径', file_path);
      if (file_path) {
        const filename = `${token.slice(0, 16)}.${fileSuffix}`;
        ctx.set('Content-disposition', `attachment;filename=${filename}`);
        ctx.set('content-type', 'text/csv; charset=utf-8');
        const data = await downloadGCPStreamFile({ fileName: file_path });
        const stream = await data.createReadStream();
        ctx.body = stream.on('error', err => {
          errorLogger.error(`pdf visit stream error=[${err.message}]`);
          throw new Error(err.message);
        });
      } else {
        ctx.body = result;
      }
    } else {
      ctx.body = result;
    }
  }
}

export const reportController = new ReportCtrl();
