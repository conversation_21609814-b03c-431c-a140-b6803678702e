import { setRedisBy<PERSON>ey } from '@/db/redis';
import {
  DemandReportV2Scheme,
  SupplyPartnerReportScheme
} from '@/schema/report';
import { partnerService } from '@/services';
import { getCtxResult } from '@/utils/validation/response';
import { validateParams } from '@/utils/validation/params';
import { Context } from 'koa';

class PartnerApiCtrl {
  @validateParams(DemandReportV2Scheme)
  async getDemandPartnerReportList(ctx: Context) {
    const api_url = ctx.originalUrl.split('?')[0];
    const { dp_id, tnt_id } = ctx.request.body;
    // 获取所有的 demand buyer id
    const buyerIds = await partnerService.getBuyerIds(dp_id, tnt_id);
    // 调用 上游的 server 的接口
    const list = await partnerService.getDemandReport(
      {
        ...ctx.request.body,
        buyer_id: buyerIds
      },
      {
        tag: api_url,
        tenantId: tnt_id
      }
    );
    if (list) {
      const { redis_count_key, redis_count, redis_expired } = ctx.state;
      await setRedisByKey(redis_count_key, redis_count, redis_expired);
      ctx.body = getCtxResult('SUCCESS', list);
      return;
    }
    ctx.body = getCtxResult('ERROR_SYS');
  }

  @validateParams(SupplyPartnerReportScheme)
  async getSupplyPartnerReportList(ctx: Context) {
    const api_url = ctx.originalUrl.split('?')[0];
    const { sp_id, tnt_id } = ctx.request.body;
    // 获取所有的 supply seller id
    const sellerIds = await partnerService.getSellerIds(sp_id, tnt_id);
    // 调用 下游的 server 的接口
    const list = await partnerService.getSupplyReport(
      {
        ...ctx.request.body,
        seller_id: sellerIds
      },
      {
        tag: api_url,
        tenantId: tnt_id
      }
    );
    if (list) {
      const { redis_count_key, redis_count, redis_expired } = ctx.state;
      await setRedisByKey(redis_count_key, redis_count, redis_expired);
      ctx.body = getCtxResult('SUCCESS', list);
      return;
    }
    ctx.body = getCtxResult('ERROR_SYS');
  }
}

export const partnerReportApiController = new PartnerApiCtrl();
