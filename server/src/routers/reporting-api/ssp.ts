/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2023-07-24 15:48:36
 * @LastEditors: chen<PERSON><PERSON>
 * @LastEditTime: 2024-01-12 15:21:33
 * @Description:
 */
import { API_TIME_OUT } from '@/constants/common';
import { sspReportApiController } from '@/controllers';
import { setApiTimeOut } from '@/middleware/time-race';
import {
  validateSidAndTokenType,
  validUser,
  validVisitDayLimit
} from '@/middleware/valid-req-params';
import Router from 'koa-router';

const { getSSPReportListV1, getSSPReportListV2 } = sspReportApiController;

const router = new Router({ prefix: '/ssp/api' })
  .use(validateSidAndTokenType)
  .use(validVisitDayLimit)
  .use(validUser);

const routers = router
  .post('/v1', setApiTimeOut(API_TIME_OUT), getSSPReportListV1)
  .get('/v1', setApiTimeOut(API_TIME_OUT), getSSPReportListV1)
  .post('/v2', setApiTimeOut(API_TIME_OUT), getSSPReportListV2)
  .get('/v2', setApiTimeOut(API_TIME_OUT), getSSPReportListV2);

export default routers;
