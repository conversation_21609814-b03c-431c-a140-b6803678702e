import { API_TIME_OUT } from '@/constants/common';
import { partnerReportApiController } from '@/controllers';
import { setApiTimeOut } from '@/middleware/time-race';
import {
  validateSidAndTokenType,
  validUser,
  validVisitDayLimit
} from '@/middleware/valid-req-params';
import Router from 'koa-router';

const { getDemandPartnerReportList, getSupplyPartnerReportList } =
  partnerReportApiController;

const router = new Router({ prefix: '/partner/api' })
  .use(validateSidAndTokenType)
  .use(validVisitDayLimit)
  .use(validUser);

const routers = router
  .get('/demand', setApiTimeOut(API_TIME_OUT), getDemandPartnerReportList)
  .post('/demand', setApiTimeOut(API_TIME_OUT), getDemandPartnerReportList)
  .get('/supply', setApiTimeOut(API_TIME_OUT), getSupplyPartnerReportList)
  .post('/supply', setApiTimeOut(API_TIME_OUT), getSupplyPartnerReportList);

export default routers;
