/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2023-07-24 15:48:36
 * @LastEditors: chen<PERSON><PERSON>
 * @LastEditTime: 2024-01-12 15:15:45
 * @Description:
 */
import { API_TIME_OUT } from '@/constants/common';
import { demandReportApiController } from '@/controllers';
import { setApiTimeOut } from '@/middleware/time-race';
import {
  validateSidAndTokenType,
  validUser,
  validVisitDayLimit
} from '@/middleware/valid-req-params';
import Router from 'koa-router';

const { getDemandReportListV1, getDemandReportListV2 } =
  demandReportApiController;

const router = new Router({ prefix: '/demand/api' })
  .use(validateSidAndTokenType)
  .use(validVisitDayLimit)
  .use(validUser);

const routers = router
  .post('/v1', setApiTimeOut(API_TIME_OUT), getDemandReportListV1)
  .get('/v1', setApiTimeOut(API_TIME_OUT), getDemandReportListV1)
  .post('/v2', setApiTimeOut(API_TIME_OUT), getDemandReportListV2)
  .get('/v2', setApiTimeOut(API_TIME_OUT), getDemandReportListV2);

export default routers;
