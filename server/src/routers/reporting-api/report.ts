/*
 * @Author: chen<PERSON><PERSON>
 * @Date: 2024-01-15 11:27:25
 * @LastEditors: chen<PERSON>dan
 * @LastEditTime: 2024-01-15 11:32:32
 * @Description:
 */
import { reportController } from '@/controllers';
import { validateTokenType } from '@/middleware/valid-req-params';
import {
  validDownloadReportToken,
  validReportTokenPermission,
  validTntId
} from '@/middleware/valid-req-params';
import Router from 'koa-router';

const { getDownloadReportUrlsV1, getReportCsv } = reportController;

const router = new Router({ prefix: '/report/api' });

const routers = router
  .post(
    '/csv/v1',
    validateTokenType,
    validTntId,
    validReportTokenPermission,
    getDownloadReportUrlsV1
  )
  .get('/file/:name', validTntId, validDownloadReportToken, getReportCsv);

export default routers;
