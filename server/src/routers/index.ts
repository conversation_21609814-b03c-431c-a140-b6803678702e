/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-01-13 14:30:12
 * @LastEditors: chen<PERSON><PERSON>
 * @LastEditTime: 2024-01-12 15:20:54
 * @Description:
 */

import fs from 'fs';
import path from 'path';
import Router from 'koa-router';
import { resolveHostPrefix } from '@/middleware/auth';
import { resolveError } from '@/middleware/common';

const routers = new Router();

const router = routers
  .use(resolveError)
  .use(resolveHostPrefix);
  // .use(validHostPrefix);

/**
 * 递归读取目录并自动生成路由
 * 
 * 该函数会递归遍历指定目录下的所有文件和子目录，自动发现并注册路由模块。
 * 这是项目的核心路由自动发现机制，遵循约定优于配置的原则。
 * 
 * @param {string} filePath - 要扫描的目录路径
 * 
 * @description 路由生成规则：
 * 
 * 1. **文件过滤规则**：
 *    - 跳过 `index.js` 和 `index.ts` 文件，避免循环引用
 *    - 只处理 `.js` 和 `.ts` 文件作为路由模块
 * 
 * 2. **目录结构**：
 *    - 目录结构仅用于组织路由文件，不影响最终的路由路径
 *    - 路由前缀完全由路由文件内部定义
 * 
 * 3. **路由模块要求**：
 *    - 每个路由文件必须导出默认的 Koa Router 实例
 *    - 路由文件应使用 `new Router({ prefix: '/api-path' })` 定义前缀
 *    - 示例结构：
 *      ```typescript
 *      const router = new Router({ prefix: '/report/api' });
 *      router.get('/v1', controller.method);
 *      export default router;
 *      ```
 * 
 * 4. **中间件应用**：
 *    - 所有自动发现的路由都会自动应用全局中间件：
 *      - `resolveError`: 错误处理中间件
 *      - `resolveHostPrefix`: 主机前缀解析中间件
 * 
 * 5. **路由注册**：
 *    - 使用 `router.use(route.routes())` 注册路由
 *    - 使用 `router.use(route.allowedMethods())` 注册允许的HTTP方法
 * 
 * @example
 * 目录结构：
 * ```
 * /routers/
 *   ├── index.ts (主路由文件)
 *   └── reporting-api/
 *       ├── report.ts (prefix: '/report/api')
 *       ├── demand.ts (prefix: '/demand/api')
 *       └── partner.ts (prefix: '/partner/api')
 * ```
 * 
 * 生成的路由：
 * - `/report/api/*` (来自 report.ts 内部定义的前缀)
 * - `/demand/api/*` (来自 demand.ts 内部定义的前缀)  
 * - `/partner/api/*` (来自 partner.ts 内部定义的前缀)
 * 
 * @throws {Error} 当路由模块导入失败或格式不正确时抛出错误
 * 
 * @since 1.0.0
 * <AUTHOR>
 */
function generateReadDir(filePath: string) {
  fs.readdirSync(filePath).forEach(async file => {
    if (file === 'index.js' || file === 'index.ts') {
      return;
    }
    const childPath = path.resolve(filePath, file);
    // 文件夹
    if (fs.statSync(childPath).isDirectory()) {
      generateReadDir(childPath);
    } else {
      const { default: route } = await import(childPath);
      router.use(route.routes()).use(route.allowedMethods());
    }
  });
}
// 递归读取当前文件夹下的文件
generateReadDir(__dirname);

export default router;
