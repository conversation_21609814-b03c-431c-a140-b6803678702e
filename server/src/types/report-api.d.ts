/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2023-07-24 16:08:43
 * @LastEditors: chenmudan
 * @LastEditTime: 2024-01-15 14:50:24
 * @Description:
 */
import { Context } from 'koa';
export declare namespace ReportAPI {
  type SSPListItem = {
    date: string;
    seller_net_revenue: number;
    request: number;
    response: number;
    impression: number;
  };
  type DemandListItem = {
    date: string;
    buyer_net_revenue: number;
    request: number;
    response: number;
    impression: number;
  };
  type SSPListParams = {
    tnt_id: number;
    day: string;
    seller_id: string;
    columns?: string[];
    start_date: string;
    end_date: string;
    tnt: number;
    dimensions: string[];
    timezone: string;
    bg_time_zone: string;
  };
  type DemandListParams = {
    tnt_id: number;
    day: string;
    buyer_id: string;
    columns?: string[];
    start_date: string;
    end_date: string;
    tnt: number;
    timezone: string;
    dimensions: string[];
    bg_time_zone: string;
  };
  type sqlValue = {
    conditions: string;
    dimension: string;
    groupBy: string;
    order?: string;
    limit?: string;
  };

  type DownloadReportParams = {
    date: string[];
    tnt_id: number;
  }
  interface SSPApiCtrl {
    getSSPReportList(ctx: Context): Promise<void>;
  }
  interface DemandApiCtrl {
    getDemandReportList(ctx: Context): Promise<void>;
  }

  interface SSPApiSerivce {
    getSSPReportList(params: SSPListParams): Promise<any>;
    initParams(params: SSPListParams): sqlValue;
  }
  interface DemandApiSerivce {
    getDemandReportList(params: DemandListParams): Promise<any>;
  }

  interface SSPApiModel {
    getSSPReportList(options: sqlValue): Promise<any>;
  }
  interface DemandApiModel {
    getDemandReportList(options: sqlValue): Promise<any>;
  }
}
