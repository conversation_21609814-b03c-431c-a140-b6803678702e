export declare namespace Utils {
  interface JoinQueryKeys {
    /**
     * 用于精确条件的字段
     */
    exactQueries?: string[];
    /**
     * 用于模糊查询的字段
     */
    fuzzyQueries?: string[];
    /**
     * 用于mysql模糊查询的字段
     */
    fuzzyQueriesForMysql?: string[];
    /**
     * 用于多个查询如 in (a,b,c)得字段
     */
    arrayQueries?: string[];
    /**
     * 数字类型的字段，会转换为数字
     */
    numberCols?: string[];
    /**
     * 额外的查询条件，直接拼接到查询条件中
     */
    extra?: string;
  }

  type JoinParams = Record<string, number | string | number[] | string[]>;
}
