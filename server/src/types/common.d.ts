/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-01-09 19:30:45
 * @LastEditors: chenmudan
 * @LastEditTime: 2024-01-15 15:12:04
 * @Description:
 */

import { Context } from 'koa';

declare namespace CommonAPI {
  type IntegrationTypeItem = {
    id: number;
    itg_name: string;
    itg_key: string;
    create_time: string;
    update_time: string;
  };

  type SessionItem = {
    user_id: number;
    account_name: string;
    tnt_id: number;
    email: string;
    tnt_name: string;
    token: string;
    cs_domain: string;
    role: number;
    session_id: string;
    isLogin: boolean;
  };

  interface Result {
    code: number;
    message: string;
    data: any;
  }

  type UploadParams = {
    fileName: string; // 上传的文件名称
    bucket?: string; // 上传的桶
    filePath: string; // 上传的文件路径
  };

  type DownloadFileParams = {
    fileName: string; // 上传的文件名称 包含路径
    bucket?: string; // 上传的桶
  };

  interface Common {
    getAllTenantHostPrefix(): Promise<any>;
  }

  interface CommonCtrl {
    getBuyerIntegrationType(ctx: Context): void;
    getSellerIntegrationType(ctx: Context): void;
  }
}
