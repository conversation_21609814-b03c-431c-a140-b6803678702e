import { queryStackOverflow } from '@/db/bigquery';
import dbUtils from '@/db/mysql';
import { buildQuerySQL } from '@/utils/database';
import { LabelGenerationParams } from '@rixfe/rix-tools';

class PartnerApiModel {
  /**
   * daily_buyer_report，查询更快
   */
  async getDemandReport(
    options: {
      condition: string;
      groupDimension: string;
      selectDimension: string;
    },
    labels: LabelGenerationParams
  ) {
    const { condition, groupDimension, selectDimension } = options;

    const sql = buildQuerySQL({
      select: [
        selectDimension ? `${selectDimension}` : '',
        `coalesce(sum(request), 0) as request`,
        `coalesce(sum(response), 0) as response`,
        `coalesce(sum(impression), 0) as impression`,
        `round(coalesce(sum(buyer_net_revenue), 0), 2) as net_revenue`
      ],
      from: 'saas-373106.saas_report_ts.daily_demand_report',
      where: condition ? [condition] : [],
      groupBy: groupDimension ? [groupDimension] : [],
      orderBy: groupDimension.includes('day') ? ['day desc'] : []
    });

    return queryStackOverflow(sql, { ...labels });
  }

  /**
   * daily_seller_report，查询更快
   */
  async getSupplyReport(
    options: {
      condition: string;
      groupDimension: string;
      selectDimension: string;
    },
    labels: LabelGenerationParams
  ) {
    const { condition, groupDimension, selectDimension } = options;

    const sql = buildQuerySQL({
      select: [
        selectDimension ? `${selectDimension}` : '',
        `(case when sum(block_request) is null then sum(request) else (sum(request) + sum(block_request)) end) as request`,
        `coalesce(sum(response), 0) as response`,
        `coalesce(sum(seller_payment_impression), 0) as impression`,
        `round(coalesce(sum(seller_net_revenue), 0), 2) as net_revenue`
      ],
      from: 'saas-373106.saas_report_ts.daily_supply_report',
      where: condition ? [condition] : [],
      groupBy: groupDimension ? [groupDimension] : [],
      orderBy: groupDimension.includes('day') ? ['day desc'] : []
    });

    return queryStackOverflow(sql, { ...labels });
  }

  async getBuyerIds(dp_id: number, tnt_id: number) {
    const buyerIdsSQL = 'select buyer_id from buyer where dp_id=? and tnt_id=?';

    const buyerIds = await dbUtils.query(buyerIdsSQL, [dp_id, tnt_id]);
    return buyerIds.map((item: any) => item.buyer_id);
  }

  async getSellerIds(sp_id: number, tnt_id: number) {
    const sellerIdsSQL =
      'select seller_id from seller where sp_id=? and tnt_id=?';

    const sellerIds = await dbUtils.query(sellerIdsSQL, [sp_id, tnt_id]);
    return sellerIds.map((item: any) => item.seller_id);
  }
}

export const partnerApiModel = new PartnerApiModel();
