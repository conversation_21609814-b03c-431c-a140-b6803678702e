/*
 * @Author: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-07-24 17:54:37
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2023-12-13 14:59:01
 * @Description:
 */
// ?utils
import { queryStackOverflow } from '@/db/bigquery';
import { buildQuerySQL } from '@/utils/database';
import { LabelGenerationParams } from '@rixfe/rix-tools';

interface SSPOptions {
  condition: string;
  groupDimension: string;
  selectDimension: string;
}

class SSPApiModel {
  async getSupplyReportByBillingTable(
    options: SSPOptions,
    labels: LabelGenerationParams
  ) {
    const { condition, groupDimension, selectDimension } = options;
    const sql = buildQuerySQL({
      select: [
        selectDimension ? `${selectDimension}` : '',
        `(case when sum(block_request) is null then sum(request) else (sum(request) + sum(block_request)) end) as request`,
        `coalesce(sum(response), 0) as response`,
        `coalesce(sum(seller_payment_impression), 0) as impression`,
        `round(coalesce(sum(seller_net_revenue), 0), 2) as net_revenue`
      ],
      from: 'saas-373106.saas_report_ts.billing_supply_overview',
      where: condition ? [condition] : [],
      groupBy: groupDimension ? [groupDimension] : [],
      orderBy: groupDimension.includes('day') ? ['day desc'] : []
    });
    return queryStackOverflow(sql, { ...labels });
  }
}

export const sspReportV2 = new SSPApiModel();
