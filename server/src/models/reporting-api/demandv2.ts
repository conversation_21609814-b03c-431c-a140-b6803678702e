/*
 * @Author: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-07-24 17:54:37
 * @LastEditors: chen<PERSON><PERSON>
 * @LastEditTime: 2023-11-21 19:21:36
 * @Description:
 */
// ?utils
import { queryStackOverflow } from '@/db/bigquery';
import { buildQuerySQL, SQLFragmentsResult } from '@/utils/database';
import { LabelGenerationParams } from '@rixfe/rix-tools';

class DemandApiModel {
  async getDemandReportByBillingTable(
    options: SQLFragmentsResult,
    labels: LabelGenerationParams
  ) {
    const { condition, groupDimension, selectDimension } = options;
    const sql = buildQuerySQL({
      select: [
        selectDimension ? `${selectDimension}` : '',
        `coalesce(sum(request), 0) as request`,
        `coalesce(sum(response), 0) as response`,
        `coalesce(sum(impression), 0) as impression`,
        `round(coalesce(sum(buyer_net_revenue), 0), 2) AS net_revenue`
      ],
      from: 'saas-373106.saas_report_ts.billing_demand_overview',
      where: condition ? [condition] : [],
      groupBy: groupDimension ? [groupDimension] : [],
      orderBy: groupDimension.includes('day') ? ['day desc'] : []
    });

    return queryStackOverflow(sql, { ...labels });
  }
}

export const demandReportV2 = new DemandApiModel();
