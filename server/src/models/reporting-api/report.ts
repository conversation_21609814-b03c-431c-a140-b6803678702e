/*
 * @Author: ch<PERSON><PERSON><PERSON>
 * @Date: 2024-01-12 17:20:34
 * @LastEditors: chenmudan
 * @LastEditTime: 2024-01-15 14:50:33
 * @Description:
 */
import dbUtils from '@/db/mysql';
import { ReportAPI } from '@/types/report-api';

class ReportModel {
  async getUserInfo(tnt_id: number, token: string) {
    const sql = 'select * from user where tnt_id=? and token=? limit 1';
    return dbUtils.query(sql, [tnt_id, token]);
  }
  // 获取已经处理了的下载链接
  async getDownloadReportUrlsV1(params: ReportAPI.DownloadReportParams) {
    const { date, tnt_id } = params;
    const sql =
      'select path, DATE_FORMAT(date, "%Y-%m-%d") as date from report_log where date in (?) and status = 1 and tnt_id=?';
    return dbUtils.query(sql, [date, tnt_id]);
  }

  async getTntInfo(host_prefix: string) {
    const sql = `select tnt_id from tenant where host_prefix=? limit 1`;
    return dbUtils.query(sql, [host_prefix]);
  }
}

export const reportModel = new ReportModel();
