/*
 * @Author: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-07-24 18:06:15
 * @LastEditors: xiezicong
 * @LastEditTime: 2024-01-04 13:07:45
 * @Description:
 */
import dbUtils from '@/db/mysql';
import { CommonAPI } from '@/types/common';

class CommonModel implements CommonAPI.Common {
  async getAllTenantHostPrefix(): Promise<{ host_prefix: string }[] | null> {
    return dbUtils.permissionQuery('select host_prefix from tenant');
  }

  // 限定用户
  async getDemandBySidToken(sid: number, token: string, host_prefix: string) {
    const sql = `
      select
        b.buyer_id,
        b.token,
        b.tnt_id,
        u.status,
        u.api_status
      from buyer as b
      left join tenant as t on t.tnt_id=b.tnt_id
      left join user as u on u.user_id=b.user_id
      where b.buyer_id=? and b.token=? and t.host_prefix=?
    `;
    return dbUtils.permissionQuery(sql, [sid, token, host_prefix]);
  }

  // 限定用户
  async getSupplyBySidToken(sid: number, token: string, host_prefix: string) {
    const sql = `
      select
        s.seller_id,
        s.token,
        s.tnt_id,
        s.integration_type,
        u.status,
        u.api_status
      from seller as s
      left join tenant as t on t.tnt_id=s.tnt_id
      left join user as u on u.user_id=s.user_id
      where s.seller_id=? and s.token=? and t.host_prefix=?
    `;
    return dbUtils.permissionQuery(sql, [sid, token, host_prefix]);
  }

  // 获取 上游 partner
  async getDemandPartnerBySidToken(
    sid: number,
    token: string,
    host_prefix: string
  ) {
    const sql =
      'select p.partner_id, p.token, p.tnt_id, p.dp_id, u.status, u.api_status from buyer_parent as p left join tenant as t on t.tnt_id=p.tnt_id left join user as u on u.user_id=p.user_id where p.partner_id=? and p.token=? and t.host_prefix=?';

    return dbUtils.permissionQuery(sql, [sid, token, host_prefix]);
  }

  async getSupplyPartnerBySidToken(
    sid: number,
    token: string,
    host_prefix: string
  ) {
    const sql =
      'select p.partner_id, p.token, p.tnt_id, p.sp_id, u.status, u.api_status from seller_parent as p left join tenant as t on t.tnt_id=p.tnt_id left join user as u on u.user_id=p.user_id where p.partner_id=? and p.token=? and t.host_prefix=?';

    return dbUtils.permissionQuery(sql, [sid, token, host_prefix]);
  }

  async getHostPrefixFromPvDomain(
    domain: string
  ): Promise<{ host_prefix: string }[]> {
    const sql = 'select host_prefix from tenant where pv_domain = ?';
    return dbUtils.permissionQuery(sql, [domain]);
  }
}
export const commonModel = new CommonModel();
