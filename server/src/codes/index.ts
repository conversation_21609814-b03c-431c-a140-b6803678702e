/*
 * @Author: ch<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2022-11-22 17:12:20
 * @LastEditors: chen<PERSON><PERSON>
 * @LastEditTime: 2023-10-13 13:58:28
 * @FilePath: /saas.rix-platform/server-ts/src/codes/index.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

const CodeSystem = {
  ERROR_SYS: 1000,
  SUCCESS: 0,
  PARAMS_INVALID: 1001,
  TOKEN_INVALID: 2000,
  REQUEST_LIMIT: 2001,
  REQUEST_COUNT_LIMIT: 2002,
  REQUEST_TIMEOUT: 2003,
  USER_STATUS_INVALID: 2004,
  TOKEN_MISSING: 2005,
  UNSUPPORTED_TNT: 2006,
  BODY_INVALID: 3000
};

const MessageSystem = {
  ERROR_SYS: 'system error',
  SUCCESS: 'success',
  PARAMS_INVALID: 'invalid params',
  TOKEN_INVALID: 'token authorized failed',
  TOKEN_MISSING: 'token missing, please add "x-authorization" header',
  UNSUPPORTED_TNT: 'unsupported tnt',
  USER_STATUS_INVALID: "user's api status is not active",
  REQUEST_LIMIT: 'request rate limit failed',
  REQUEST_COUNT_LIMIT: 'The number of requests for the day has reached the upper limit',
  REQUEST_TIMEOUT: 'request timeout',
  BODY_INVALID: 'data request body invalid'
};

export const Code = {
  ...CodeSystem
};
export const Message = {
  ...MessageSystem
};
export type CodeProps = keyof typeof Code;
export default {};
