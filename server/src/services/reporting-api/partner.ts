import { demandReportV2, partnerApiModel, sspReportV2 } from '@/models';
import { generateReportSQLFragments } from '@/utils/database';
import { LabelGenerationParams } from '@rixfe/rix-tools';

class PartnerApiService {
  async getSupplyReport(data: any, labels: LabelGenerationParams) {
    // const { timezone } = data;
    const options = generateReportSQLFragments(
      {
        keys: {
          numberCols: ['seller_id', 'tnt_id'],
          exactQueries: ['tnt_id'],
          arrayQueries: ['seller_id']
        },
        data: data
      },
      {
        specialId: 'seller_id'
        // timePrecision: timezone === 'UTC+0' ? 'day' : 'hour'
      }
    );

    // 根据时区选择不同的数据获取方式
    // const list =
    //   timezone === 'UTC+0'
    //     ? await partnerApiModel.getSupplyReport(options, labels)
    //     : await sspReportV2.getSupplyReportByBillingTable(options, labels);
    const list = await sspReportV2.getSupplyReportByBillingTable(
      options,
      labels
    );

    return {
      total: list?.length || 0,
      data: list
    };
  }

  async getDemandReport(data: any, labels: LabelGenerationParams) {
    // const { timezone } = data;
    const options = generateReportSQLFragments(
      {
        keys: {
          numberCols: ['buyer_id', 'tnt_id'],
          exactQueries: ['tnt_id'],
          arrayQueries: ['buyer_id']
        },
        data: data
      },
      {
        specialId: 'buyer_id'
        // timePrecision: timezone === 'UTC+0' ? 'day' : 'hour'
      }
    );

    // 根据时区选择不同的数据获取方式
    // const list =
    //   timezone === 'UTC+0'
    //     ? await partnerApiModel.getDemandReport(options, labels)
    //     : await demandReportV2.getDemandReportByBillingTable(options, labels);
    const list = await demandReportV2.getDemandReportByBillingTable(
      options,
      labels
    );

    return {
      total: list?.length || 0,
      data: list
    };
  }

  async getBuyerIds(dp_id: number, tnt_id: number) {
    return partnerApiModel.getBuyerIds(dp_id, tnt_id);
  }

  async getSellerIds(sp_id: number, tnt_id: number) {
    return partnerApiModel.getSellerIds(sp_id, tnt_id);
  }
}

export const partnerService = new PartnerApiService();
