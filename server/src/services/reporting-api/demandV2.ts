/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2023-07-24 16:15:44
 * @LastEditors: chenmudan
 * @LastEditTime: 2023-11-28 09:53:49
 * @Description:
 */
import { demandReportV2 } from '@/models';
import { generateReportSQLFragments, SQLFragmentsData } from '@/utils/database';
import { LabelGenerationParams } from '@rixfe/rix-tools';

class DemandApiService {
  // v1 与 v2 公用 区别是 condition不一样
  async getDemandReportV2(
    params: SQLFragmentsData,
    labels: LabelGenerationParams
  ) {
    const options = generateReportSQLFragments(
      {
        keys: {
          numberCols: ['buyer_id', 'tnt_id'],
          exactQueries: ['tnt_id'],
          arrayQueries: ['buyer_id']
        },
        data: params
      },
      {
        specialId: 'buyer_id'
      }
    );
    const data = await demandReportV2.getDemandReportByBillingTable(
      options,
      labels
    );
    return { total: data?.length || 0, data };
  }
}

export const demandServiceV2 = new DemandApiService();
