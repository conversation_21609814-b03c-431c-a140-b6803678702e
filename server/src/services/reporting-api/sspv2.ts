/*
 * @Author: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-07-24 16:15:44
 * @LastEditors: chen<PERSON>dan
 * @LastEditTime: 2023-11-28 09:53:57
 * @Description:
 */

import { sspReportV2 } from '@/models';
import { generateReportSQLFragments } from '@/utils/database';
import { LabelGenerationParams } from '@rixfe/rix-tools';

class SSPApiService {
  // v1 与 v2 公用 区别是 condition不一样
  async getSupplyReportV2(formData: any, labels: LabelGenerationParams) {
    // 判断 formData 的 dimensions 是否含有 app_bundle_id，placement_id
    // 如果有，查大表，否则查 billing 表
    // const hasAppAndPlacement =
    //   formData.dimensions?.includes('app_bundle_id') ||
    //   formData.dimensions?.includes('placement_id');

    const options = generateReportSQLFragments(
      {
        keys: {
          numberCols: ['seller_id', 'tnt_id'],
          exactQueries: ['tnt_id'],
          arrayQueries: ['seller_id']
        },
        data: formData
      },
      {
        specialId: 'seller_id'
        // 大表使用 tnt, billing 表使用 tnt_id
        // isTntId: !hasAppAndPlacement
      }
    );
    // if (hasAppAndPlacement) {
    //   const data = await sspReportV2.getSupplyReportV2(options, labels);
    //   return { total: data?.length || 0, data };
    // } else {
    const data = await sspReportV2.getSupplyReportByBillingTable(
      options,
      labels
    );
    return { total: data?.length || 0, data };
    // }
  }
}

export const sspServiceV2 = new SSPApiService();
