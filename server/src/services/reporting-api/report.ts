/*
 * @Author: ch<PERSON><PERSON><PERSON>
 * @Date: 2024-01-12 17:24:42
 * @LastEditors: chenmudan
 * @LastEditTime: 2024-01-16 12:31:58
 * @Description:
 */

import { setMulRedisByKey } from '@/db/redis';
import { reportModel } from '@/models';
import { ReportAPI } from '@/types/report-api';
import { getConfig, md5 } from '@/utils';
import { encrypt } from '@/utils/config';
import moment from 'moment';

const { redisConfig } = getConfig();

class CustomReport {
  async getUserInfo(tnt_id: number, token: string) {
    return reportModel.getUserInfo(tnt_id, token);
  }
  async getDownloadReportUrlsV1(
    params: ReportAPI.DownloadReportParams,
    originalUrl: string,
    userToken: string
  ) {
    const result = await reportModel.getDownloadReportUrlsV1(params);
    if (Array.isArray(result) && result.length) {
      const arr = result.map(v => {
        const token = md5(
          `${redisConfig.get_report_csv_token_key}_${new Date().getTime()}_${
            v.path
          }`
        );
        const auth = encrypt(`${userToken}_${token}`);
        return {
          date: v.date,
          url: `${originalUrl}/report/api/file/${auth}.csv`,
          path: v.path,
          token: auth
        };
      });
      // Mon Jan 15 05:03:16 +0000 2024
      // Mon 000 Mo 05:01:46 o 2024
      const data = arr.map(v => {
        return {
          path: v.url,
          date: v.date,
          expire_time: moment()
            .add(1, 'hours')
            .format('ddd MMM DD HH:mm:ss Z yyyy')
        };
      });
      const expired_time = 1 * 60 * 60;
      const redisArr = arr.map(v => ({
        key: v.token,
        value: v.path,
        secs: expired_time
      }));
      await setMulRedisByKey(redisArr);
      return data;
    }
    return result;
  }

  async getTntInfo(host_prefix: string) {
    return reportModel.getTntInfo(host_prefix);
  }
}

export const reportService = new CustomReport();
