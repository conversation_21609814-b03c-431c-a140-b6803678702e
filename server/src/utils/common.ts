import devConfig from '@/config/dev.env.config';
import { appLogger, errorLogger } from '@/config/log4js';
import prodConfig from '@/config/prod.env.config';
import testConfig from '@/config/test.env.config';
import { isNotEmptyStr, trim } from '@/utils/string';
import crypto from 'crypto';
import nodemailer from 'nodemailer';
import SMTPTransport from 'nodemailer/lib/smtp-transport';

/**
 * 获取环境配置
 * @returns 环境配置对象
 */
export const getConfig = function () {
  if (!process.env.NODE_ENV) {
    throw Error(`environment variable 'NODE_ENV' is required.`);
  }
  console.log('xx当前的环境', process.env.TZ, process.env.NODE_ENV);
  let result = prodConfig;
  switch (process.env.NODE_ENV) {
    case 'prod':
      result = prodConfig;
      break;
    case 'test':
      result = testConfig;
      break;
    default:
      result = devConfig;
      break;
  }
  return result;
};

/**
 * MD5 加密函数
 * @param content 要加密的内容
 * @returns 加密后的十六进制字符串
 */
export const md5 = function (content: crypto.BinaryLike) {
  const hash = crypto.createHash('md5');
  return hash.update(content).digest('hex');
};

/**
 * 生成随机密码
 * @param pasLen 密码长度
 * @returns 随机密码
 */
export const genEnCode = function (pasLen: number) {
  const pasArr = [
    'a',
    'b',
    'c',
    'd',
    'e',
    'f',
    'g',
    'h',
    'i',
    'j',
    'k',
    'l',
    'm',
    'n',
    'o',
    'p',
    'q',
    'r',
    's',
    't',
    'u',
    'v',
    'w',
    'x',
    'y',
    'z',
    'A',
    'B',
    'C',
    'D',
    'E',
    'F',
    'G',
    'H',
    'I',
    'J',
    'K',
    'L',
    'M',
    'N',
    'O',
    'P',
    'Q',
    'R',
    'S',
    'T',
    'U',
    'V',
    'W',
    'X',
    'Y',
    'Z',
    '0',
    '1',
    '2',
    '3',
    '4',
    '5',
    '6',
    '7',
    '8',
    '9',
    '_',
    '-',
    '$',
    '%',
    '&',
    '@',
    '+',
    '!'
  ];
  let password = '';
  const pasArrLen = pasArr.length;
  for (let i = 0; i < pasLen; i++) {
    password += pasArr[Math.floor(Math.random() * pasArrLen)];
  }
  return password;
};

export { isNotEmptyStr, trim };

export const EmailParams = {
  from: 'Rix Engine<<EMAIL>>',
  // cc: process.env.NODE_ENV === 'prod' ? '<EMAIL>' : '<EMAIL>'
  cc:
    process.env.NODE_ENV === 'prod'
      ? 'Rix Engine<<EMAIL>>'
      : 'Rix Engine<<EMAIL>>'
};

const { emailConfig } = getConfig();
const mailParams: SMTPTransport | SMTPTransport.Options = {
  host: emailConfig.host,
  port: emailConfig.port,
  secure: false,
  debug: true,
  requireTLS: true,
  auth: {
    user: emailConfig.user,
    pass: emailConfig.pass
  },
  tls: {
    ciphers: 'SSLv3'
  }
};

const transporter = nodemailer.createTransport(mailParams);
export const sendEmail = async function sendEmail(obj: any) {
  // send mail with defined transport object
  const { from } = EmailParams;
  await transporter.sendMail({ from, ...obj }, (error, info) => {
    if (error) {
      errorLogger.error(error);
    } else {
      appLogger.info(`Message sent: ${info.messageId}`);
    }
  });
};
