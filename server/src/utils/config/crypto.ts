import crypto from 'crypto';

/**
 * MD5 加密函数
 * @param content 要加密的内容
 * @returns 加密后的十六进制字符串
 */
export const md5 = function (content: crypto.BinaryLike) {
  const hash = crypto.createHash('md5');
  return hash.update(content).digest('hex');
};

// 配置密钥和算法（建议密钥长度为 32 字节）
const SECRET_KEY = 'h0ivq48W5Z1nTvtt63v9MFfS3hDUuvZ2';
const ALGORITHM = 'aes-256-cbc';

/**
 * 加密函数
 * @param data 要加密的内容
 * @returns 固定长度的加密字符串（Base64 编码）
 */
export function encrypt(data: string): string {
  const iv = crypto.randomBytes(16); // 生成随机 IV
  const cipher = crypto.createCipheriv(ALGORITHM, Buffer.from(SECRET_KEY), iv);
  let encrypted = cipher.update(data, 'utf8', 'base64');
  encrypted += cipher.final('base64');
  // 将 IV 和密文组合并 Base64 编码，保证双向解密能力
  const combined = `${iv.toString('hex')}:${encrypted}`;
  return Buffer.from(combined).toString('base64');
}

/**
 * 解密函数
 * @param encryptedData 加密后的字符串
 * @returns 解密后的原始内容
 */
export function decrypt(encryptedData: string): string {
  // 解码并分离 IV 和密文
  const decoded = Buffer.from(encryptedData, 'base64').toString('utf8');
  const [ivHex, encrypted] = decoded.split(':');
  const iv = Buffer.from(ivHex, 'hex');
  const decipher = crypto.createDecipheriv(
    ALGORITHM,
    Buffer.from(SECRET_KEY),
    iv
  );
  let decrypted = decipher.update(encrypted, 'base64', 'utf8');
  decrypted += decipher.final('utf8');
  return decrypted;
}
