import devConfig from '@/config/dev.env.config';
import prodConfig from '@/config/prod.env.config';
import testConfig from '@/config/test.env.config';

/**
 * 获取环境配置
 * @returns 环境配置对象
 */
export const getConfig = function () {
  if (!process.env.NODE_ENV) {
    throw Error(`environment variable 'NODE_ENV' is required.`);
  }
  console.log('xx当前的环境', process.env.TZ, process.env.NODE_ENV);
  let result = prodConfig;
  switch (process.env.NODE_ENV) {
    case 'prod':
      result = prodConfig;
      break;
    case 'test':
      result = testConfig;
      break;
    default:
      result = devConfig;
      break;
  }
  return result;
};
