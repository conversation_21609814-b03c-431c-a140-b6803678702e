// 报警通知
import request from '@/utils/http/client';

const StaticUrl =
  'https://open.feishu.cn/open-apis/bot/v2/hook/2842f829-b85d-4a43-9964-dc99c82f5dcc';
const TestStaticUrl =
  'https://open.feishu.cn/open-apis/bot/v2/hook/60030935-5897-491d-bd6f-9f39145b9787';

const NotifyUrl = process.env.NODE_ENV === 'prod' ? StaticUrl : TestStaticUrl;

const DevEnv = ['dev', 'development'];
export const handleAlertToFeiShu = (title: string, content: string) => {
  const params = {
    msg_type: 'post',
    content: {
      post: {
        zh_cn: {
          title,
          content: [[{ tag: 'text', text: content }]]
        }
      }
    }
  };
  if (!DevEnv.includes(process.env.NODE_ENV || 'test')) {
    request.post(NotifyUrl, params, {
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
};
