import { type Context } from 'koa';
import qs from 'qs';

/**
 * 从 Koa 上下文中解析请求参数。
 * 如果是 POST 请求，则返回请求体；否则返回查询参数。
 * 会自动处理时区参数中的空格，将其替换为"+"。
 * @param ctx - Koa 上下文对象。
 * @returns 解析后的请求参数对象。
 */
export function getRequestParams(ctx: Context) {
  if (ctx.request.method.toLowerCase() === 'post') {
    return ctx.request.body || {};
  }
  const query = qs.parse(ctx.request.querystring);

  if (typeof query.timezone === 'string') {
    query.timezone = query.timezone.replace(/ /g, '+');
  } else if (Array.isArray(query.timezone)) {
    query.timezone = query.timezone.map(item =>
      typeof item === 'string' ? item.replace(/ /g, '+') : item
    );
  }

  return query || {};
}
