import { errorLogger } from '@/config/log4js';
import axios from 'axios';

const request = axios.create({
  timeout: 120000
});

request.interceptors.response.use(
  resp => {
    if (resp.status !== 200 || !resp.data) {
      return Promise.reject(resp);
    }
    return resp.data;
  },
  err => {
    errorLogger.error(
      `request failed, err=[${JSON.stringify(err)}, ${err.message}]`
    );
    Promise.reject(err);
  }
);

request.interceptors.request.use(req => req);

export default request;
