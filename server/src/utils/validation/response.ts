import moment from 'moment';
import { Code, Message, CodeProps } from '@/codes';

// 获取通用的返回结果
export const getCtxResult = function (
  err_key: CodeProps,
  data?: any,
  errmsg?: string
) {
  return {
    status: {
      code: Code[err_key],
      message: errmsg || Message[err_key]
    },
    timestamp: moment.utc(new Date()).format('ddd MMM D HH:mm:ss Z YYYY'),
    data: data || {}
  };
};

export const getCtxBackResult = function (
  err_key: CodeProps,
  data: any,
  errmsg?: string
) {
  return {
    code: Code[err_key],
    message: errmsg || Message[err_key],
    data: {
      data: data.data || {},
      total: data.total || 0
    }
  };
};