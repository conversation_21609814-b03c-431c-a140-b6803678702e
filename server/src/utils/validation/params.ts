import { Message } from '@/codes';
import { appLogger } from '@/config/log4js';
import { getRequestParams } from '@/utils/http/parser';
import { getCtxResult } from '@/utils/validation/response';
import <PERSON><PERSON> from 'joi';
import { Context, Next } from 'koa';

// 验证参数 验证post/get参数
export const validateParams =
  (schema: Joi.Schema) =>
  (target: any, methodName: string, desc: PropertyDescriptor) => {
    const curMethod = desc.value; // 原方法
    // eslint-disable-next-line no-param-reassign
    desc.value = async (ctx: Context, next: Next) => {
      // 参数都放ctx.request.body了
      const params = getRequestParams(ctx);
      appLogger.info(`query params=[${JSON.stringify(params)}]`);
      // 使用schema中定义的unknown选项，不再默认允许多余字段
      const result = schema.validate(params || {}, { allowUnknown: false });
      if (result.error || !params) {
        const res = getCtxResult('PARAMS_INVALID', {});
        res.status.message = result.error?.message || Message.PARAMS_INVALID;
        ctx.body = res;
        return;
      }
      // 执行原方法
      const { user_info, bg_time_zone, timezone } = ctx.state;
      ctx.request.body = {
        ...params,
        ...user_info,
        bg_time_zone,
        timezone
      };
      await curMethod.call(target, ctx, next);
    };
  };
