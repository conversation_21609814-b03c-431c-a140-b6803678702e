// 工具函数类型定义
export interface BuildSQLData {
  select: string[];
  from: string;
  joins?: string[];
  where?: string[];
  groupBy?: string[];
  having?: string[];
  orderBy?: string[];
  limit?: number | null;
}

export interface SQLFragmentsData {
  tnt_id: number;
  start_date: string;
  end_date: string;
  dimensions: string[];
  bg_time_zone?: string;
  seller_id?: number | number[];
  buyer_id?: number | number[];
}

export interface SQLFragmentsExtraOptions {
  /**
   * 特殊字段
   * @description 默认为 'buyer_id'
   */
  specialId?: 'seller_id' | 'buyer_id';
  /**
   * 时间精度
   * @description 默认为 'hour'
   */
  timePrecision?: 'day' | 'hour';
  /**
   * 是否使用 tnt_id, 默认为 true
   * @description 大表使用 tnt, billing 或 daily 使用 tnt_id
   */
  isTntId?: boolean;
}

export interface SQLFragmentsResult {
  condition: string;
  groupDimension: string;
  selectDimension: string;
}

export interface ExtendBQQueryLabels {
  /**
   * 用户角色 internal | external
   */
  userRole?: string;
  /**
   * 查询的 sql
   */
  sql: string;
}