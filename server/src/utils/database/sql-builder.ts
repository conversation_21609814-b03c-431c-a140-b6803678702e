interface BuildSQLData {
  select: string[];
  from: string;
  joins?: string[];
  where?: string[];
  groupBy?: string[];
  having?: string[];
  orderBy?: string[];
  limit?: number | null;
}

/**
 * 构建 SQL 查询语句
 * @param params BuildSQLData
 * @returns SQL 查询语句
 */
export function buildQuerySQL(params: BuildSQLData): string {
  const {
    select,
    from,
    joins = [],
    where = [],
    groupBy = [],
    having = [],
    orderBy = [],
    limit
  } = params;
  const sqlParts: string[] = [];

  // Build SELECT clause
  if (select.length > 0) {
    sqlParts.push(`SELECT ${select.filter(Boolean).join(', ')}`);
  } else {
    throw new Error('SELECT clause is required.');
  }

  // Build FROM clause
  if (from) {
    sqlParts.push(`FROM ${from}`);
  } else {
    throw new Error('FROM clause is required.');
  }

  // Build JOIN clauses if any
  if (joins.length > 0 && joins.some(<PERSON><PERSON><PERSON>)) {
    sqlParts.push(joins.filter(Boolean).join(' '));
  }

  // Build WHERE clause
  if (where.length > 0 && where.some(Boolean)) {
    sqlParts.push(`WHERE ${where.filter(Boolean).join(' AND ')}`);
  }

  // Build GROUP BY clause
  if (groupBy.length > 0 && groupBy.some(Boolean)) {
    sqlParts.push(`GROUP BY ${groupBy.filter(Boolean).join(', ')}`);
  }

  // Build HAVING clause
  if (having.length > 0 && having.some(Boolean)) {
    sqlParts.push(`HAVING ${having.filter(Boolean).join(' AND ')}`);
  }

  // Build ORDER BY clause
  if (orderBy.length > 0 && orderBy.some(Boolean)) {
    sqlParts.push(`ORDER BY ${orderBy.filter(Boolean).join(', ')}`);
  }

  // Build LIMIT clause
  if (limit !== null && limit !== undefined) {
    sqlParts.push(`LIMIT ${limit}`);
  }

  // Join all parts with a space and return the SQL string
  return sqlParts.join(' ');
}
