import { Utils } from '@/types/utils';
import { isNotEmptyStr, trim } from '@/utils/common';

const isValidValue = (value: any): boolean =>
  typeof value !== 'undefined' && !!trim(value);

const formatValue = (value: any, isNum: boolean) =>
  isNum ? `${value}` : `'${value}'`;

/**
 * 精确查询
 * @param key 表字段
 * @param value 值 字符串格式化为key = "value"
 * @param isNum 是否数字
 * @returns 查询条件字符串
 */
export const exactQueries = (
  key: string | number,
  value: any,
  isNum: boolean
): string =>
  isValidValue(value) ? `${key} = ${formatValue(value, isNum)}` : '';

/**
 * 模糊查询（正则表达式）
 * @param key 表字段
 * @param value 值
 * @returns 查询条件字符串
 */
export const fuzzyQueries = (key: string | number, value: any): string =>
  isValidValue(value) ? `${key} ~* '${value}'` : '';

/**
 * MySQL 模糊查询
 * @desc MySQL中使用LIKE进行模糊查询
 */
export const fuzzyQueriesForMysql = (
  key: string | number,
  value: any
): string => (isValidValue(value) ? `${key} like '%${value}%'` : '');

/**
 * 数组查询
 * @param key 表字段
 * @param value 值 格式化为 in (a,b,c) 格式
 * @param isNum 是否数字 结果 in (1,2,3) 或 in ('a','b','c')
 * @returns 查询条件字符串
 */
export const arrayQueries = (
  key: string | number,
  value: any[],
  isNum: boolean
): string => {
  if (Array.isArray(value) && value.length > 0) {
    const content = value.map(item => formatValue(item, isNum)).join(',');
    return `${key} IN (${content})`;
  }
  return '';
};

/**
 * @description 生成查询字符串
 * @param obj 数据对象
 * @param numberCols 数字类型的字段，会转换为数字
 * @param items 字段数组，哪些字段需要处理
 * @param queryFunc 字段处理函数
 * @returns 查询字符串
 */
const generateQueryString = (
  obj: Utils.JoinParams,
  numberCols: (string | number)[],
  items: (string | number)[] | undefined,
  queryFunc: (item: string | number, value: any, isNumber: boolean) => string
) =>
  items
    ?.map(item => queryFunc(item, obj[item], numberCols.includes(item)))
    .filter(isNotEmptyStr)
    .join(' AND ')
    .trim() || '';

/**
 * @description keys = {exactQueries: 用于精确条件的字段, arrayQueries:用于多个查询如 in (a,b,c)得字段，numberCols: 数字类型的字段}
 * @param keys 字段数组
 * @param obj 数据对象
 * @returns 查询条件字符串
 */
export const joinQueries = function (
  keys: Utils.JoinQueryKeys,
  obj: Utils.JoinParams
) {
  const queries: string[] = keys.extra ? [keys.extra] : [];
  const numberCols = keys.numberCols || [];

  // 精确查询
  const exactQueriesStr = generateQueryString(
    obj,
    numberCols,
    keys.exactQueries,
    exactQueries
  );

  // 模糊查询
  const fuzzyQueriesStr =
    generateQueryString(
      obj,
      numberCols,
      keys.fuzzyQueriesForMysql,
      fuzzyQueries
    ) || generateQueryString(obj, numberCols, keys.fuzzyQueries, fuzzyQueries);

  // 数组查询
  const arrayQueriesStr = generateQueryString(
    obj,
    numberCols,
    keys.arrayQueries,
    arrayQueries
  );

  // 拼接查询条件
  [exactQueriesStr, fuzzyQueriesStr, arrayQueriesStr]
    .filter(isNotEmptyStr)
    .forEach(query => queries.push(query));

  return queries.join(' AND ').trim();
};

/**
 * @description 拼接字符串
 * @param strList 字符串数组
 * @param connector 连接符
 * @returns 拼接后的字符串
 */
export const joinStr = function (strList: any[], connector: any) {
  return trim(strList.filter(isNotEmptyStr).join(connector));
};
