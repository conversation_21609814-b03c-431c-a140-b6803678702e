import { Utils } from '@/types/utils';
import { joinQueries } from '@/utils/database/query-params';
import moment from 'moment-timezone';

export interface SQLFragmentsData {
  tnt_id: number;
  start_date: string;
  end_date: string;
  dimensions: string[];
  bg_time_zone?: string;
  seller_id?: number | number[];
  buyer_id?: number | number[];
}

interface SQLFragments {
  keys: Utils.JoinQueryKeys;
  data: SQLFragmentsData;
}

export interface SQLFragmentsExtraOptions {
  /**
   * 特殊字段
   * @description 默认为 'buyer_id'
   */
  specialId?: 'seller_id' | 'buyer_id';
  /**
   * 时间精度
   * @description 默认为 'hour'
   */
  timePrecision?: 'day' | 'hour';
  /**
   * 是否使用 tnt_id, 默认为 true
   * @description 大表使用 tnt, billing 或 daily 使用 tnt_id
   */
  isTntId?: boolean;
}

export interface SQLFragmentsResult {
  condition: string;
  groupDimension: string;
  selectDimension: string;
}

const timeFormatHour = 'YYYY-MM-DD HH:mm:ss';
const timeFormatDay = 'YYYY-MM-DD';

/**
 * 格式化日期: 一天的开始时间或结束时间
 * @param date 日期
 * @param timezone 时区
 * @param isEndOfDay 是否为一天的结束时间
 * @param timePrecision 时间精度，可以为 'day' 或 'hour'
 * @returns 格式化后的日期字符串
 */
export const formatDateRange = (
  date: string,
  timezone: string,
  isEndOfDay: boolean,
  timePrecision: 'day' | 'hour'
): string => {
  // 解析日期
  const momentDate = moment.tz(date, timezone);
  // 检查日期是否有效
  if (!momentDate.isValid()) {
    throw new Error('Invalid date format');
  }
  // 根据 isEndOfDay 确定是开始时间还是结束时间
  const formattedDate = isEndOfDay
    ? momentDate.endOf('day')
    : momentDate.startOf('day');

  return formattedDate
    .utc()
    .format(timePrecision === 'day' ? timeFormatDay : timeFormatHour);
};

/**
 * 生成分组维度 SQL
 * @param dimensions 维度数组
 * @param timePrecision 时间精度
 * @returns 分组维度 SQL 字符串
 */
const generateGroupDimension = (
  dimensions: string[],
  timePrecision: 'day' | 'hour'
): string => {
  return (
    dimensions
      // 移除 specialId
      .filter(
        dimension => dimension !== 'seller_id' && dimension !== 'buyer_id'
      )
      // 把 day 放到最前面
      .sort(a => (a === 'day' ? -1 : 1))
      .join(',')
  );
};

/**
 * 生成选择维度 SQL
 * @param dimensions 维度数组
 * @param specialId 特殊字段
 * @param timePrecision 时间精度
 * @param bg_time_zone 时区
 * @returns 选择维度 SQL 字符串
 */
const generateSelectDimension = (
  dimensions: string[],
  specialId: string,
  timePrecision: 'day' | 'hour',
  bg_time_zone: string
): string => {
  return dimensions
    .filter(dimension => dimension !== specialId)
    .map(dimension => {
      if (dimension === 'day') {
        return timePrecision === 'hour'
          ? `FORMAT_DATETIME('%Y-%m-%d', DATETIME(day_hour, '${bg_time_zone}')) as day`
          : `FORMAT_DATE('%Y-%m-%d', date) AS day`;
      }
      return dimension;
    })
    .join(',');
};

/**
 * 根据传入的参数生成 SQL 查询片段
 * @param params 查询参数
 * @returns SQL 片段
 */
export function generateReportSQLFragments(
  params: SQLFragments,
  options?: SQLFragmentsExtraOptions
): SQLFragmentsResult {
  const { keys, data } = params;
  const {
    specialId = 'buyer_id',
    timePrecision = 'hour',
    isTntId = true
  } = options || {};

  const {
    start_date,
    end_date,
    dimensions = [],
    bg_time_zone = 'Etc/UTC'
  } = data;

  const startDateFormatted = formatDateRange(
    start_date,
    bg_time_zone,
    false,
    timePrecision
  );
  const endDateFormatted = formatDateRange(
    end_date,
    bg_time_zone,
    true,
    timePrecision
  );

  const formData = {
    ...data,
    [specialId]: Array.isArray(data[specialId])
      ? data[specialId]
      : [data[specialId]]
  };

  // 处理维度，如果维度数组为空，默认添加 'day' 和 specialId
  formData.dimensions =
    Array.isArray(dimensions) && dimensions.length
      ? [...new Set([...formData.dimensions, specialId])]
      : ['day', specialId];

  // 定义 SQL 查询片段
  const sqlFragments = {
    condition: '',
    groupDimension: '',
    selectDimension: ''
  };

  const extraTimePrecision = timePrecision === 'hour' ? 'day_hour' : 'date';

  let extra = `${extraTimePrecision} >= '${startDateFormatted}' and ${extraTimePrecision} <= '${endDateFormatted}'`;

  if (timePrecision === 'hour') {
    let startDay = formatDateRange(start_date, bg_time_zone, false, 'day');
    let endDay = formatDateRange(end_date, bg_time_zone, true, 'day');
    extra += ` and date >= '${startDay}' and date <= '${endDay}'`;
  }

  // 生成查询条件 SQL
  sqlFragments.condition = joinQueries(
    {
      extra,
      ...keys
    },
    formData
  );

  // 特殊逻辑：由于 daily 的 租户字段 为 tnt_id,而普通的为 tnt
  sqlFragments.condition = isTntId
    ? sqlFragments.condition
    : sqlFragments.condition.replace(/tnt_id/g, 'tnt');

  // 生成分组维度 SQL
  sqlFragments.groupDimension = generateGroupDimension(
    formData.dimensions,
    timePrecision
  );

  // 生成选择维度 SQL
  sqlFragments.selectDimension = generateSelectDimension(
    formData.dimensions,
    specialId,
    timePrecision,
    bg_time_zone
  );

  return sqlFragments;
}
