import { errorLogger } from '@/config/log4js';
import { CommonAPI } from '@/types/common';
import { Storage } from '@google-cloud/storage';

const storage = new Storage();

// filePath是绝对路径 fileName是上传后的文件名称
export async function uploadFile(params: CommonAPI.UploadParams) {
  const bucket = params.bucket || 'console-rix-engine';
  const options = {
    destination: params.fileName
  };
  try {
    await storage.bucket(bucket).upload(params.filePath, options);
    return Promise.resolve(1);
  } catch (error: any) {
    errorLogger.error(`gcp uploadFile failed err=[${error.message}]`);
    console.log('xx报错', error);
    return Promise.reject(error);
  }
}

export async function downloadGCPStreamFile(
  params: CommonAPI.DownloadFileParams
) {
  const bucket = params.bucket || 'console-rix-engine';
  const { fileName } = params;
  return storage.bucket(bucket).file(fileName);
}
