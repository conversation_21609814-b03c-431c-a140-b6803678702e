import {
  generateBQLabels,
  LabelGenerationParams,
  REPORT_SCHEMA
} from '@rixfe/rix-tools';

const env = process.env.NODE_ENV === 'prod' ? '' : 'test_';

/**
 * 获取 bigquery 的 labels
 * @param params 标签参数
 * @param sql 查询的 sql
 * @returns 标签对象
 */
export function getBQLabels(params: LabelGenerationParams, sql: string) {
  return generateBQLabels(extendBQQueryLabels(params, { sql }), {
    envPrefix: env
  });
}

/**
 * 通过 查询的 sql 获取 查询的表名
 */
export function getBQTableName(sql: string): string {
  // 表名前缀：REPORT_SCHEMA，使用了 捕获组 来获取表名
  // 在多表名的查询，只能获取第一个表名
  const regex = new RegExp(`FROM\\s+${REPORT_SCHEMA}\\.(\\w+)`, 'i');
  const match = sql.match(regex);

  // match 数组最后一项就是 表名，需要处理 null 的情况
  return match ? match[match.length - 1] : '';
}

interface ExtendBQQueryLabels {
  /**
   * 用户角色 internal | external
   */
  userRole?: string;
  /**
   * 查询的 sql
   */
  sql: string;
}

/**
 * 扩展 bigquery 的 labels
 * @param labels 原始 labels
 * @param extend 扩展参数
 * @returns 扩展后的标签参数
 */
export function extendBQQueryLabels(
  labels: LabelGenerationParams,
  extend: ExtendBQQueryLabels
): LabelGenerationParams {
  // labels 原状只有 tag 和 tenantId，现在需要扩展 userRole 和 table
  const { userRole = 'external', sql = '' } = extend;
  return {
    userRole,
    ...labels,
    table: getBQTableName(sql)
  };
}