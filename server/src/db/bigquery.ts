/*
 * @Author: 袁跃钊 yuanyu<PERSON><PERSON>@algorix.co
 * @Date: 2023-07-11 09:02:29
 * @LastEditors: chen<PERSON>dan
 * @LastEditTime: 2023-11-20 16:22:32
 * @Description:
 */

import { errorLogger, sqlLogger } from '@/config/log4js';
import { getBQLabels } from '@/utils/database';
import { BigQuery, Query } from '@google-cloud/bigquery';
import { LabelGenerationParams } from '@rixfe/rix-tools';

const bigqueryClient = new BigQuery();

export async function queryStackOverflow(
  sql: string,
  opt: LabelGenerationParams
) {
  const labels = getBQLabels(opt, sql);
  const stringifyLabels = JSON.stringify(labels);

  try {
    console.log('queryStackOverflow', sql);
    const options: Query = {
      query: sql,
      location: 'us-east4',
      labels
    };

    // Run the query
    const [rows] = await bigqueryClient.query(options);
    sqlLogger.log(
      `queryStackOverflow--BigQuery sql=[${sql}], tag=[${stringifyLabels}]`
    );

    return Promise.resolve(rows);
  } catch (error: any) {
    console.log('queryStackOverflow error', error?.message || error);
    errorLogger.log(
      `queryStackOverflow error: ${sql} ${stringifyLabels} ${
        error?.message || error
      }`
    );
    return Promise.reject(error);
  }
}

export async function bigqueryStream(sql: string, opt: LabelGenerationParams) {
  let stream;
  try {
    const options: Query = {
      query: sql,
      location: 'us-east4',
      labels: getBQLabels(opt, sql)
    };
    stream = await bigqueryClient.createQueryStream(options);
    return Promise.resolve(stream);
  } catch (error: any) {
    console.log('bigqueryStream error', error?.message || error);
    errorLogger.log(`bigqueryStream error: ${error?.message || error}`);
    return Promise.reject(error);
  }
}
