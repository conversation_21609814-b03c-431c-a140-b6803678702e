/*
 * @Author: ch<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-03-07 16:25:48
 * @LastEditors: chenmudan
 * @LastEditTime: 2024-01-16 11:49:35
 * @Description:
 */

import Redis from 'ioredis';
import { getConfig } from '@/utils/common';
import { errorLogger, appLogger } from '@/config/log4js';

const { redisConfig } = getConfig();

const redis = redisConfig.is_cluster
  ? new Redis.Cluster(redisConfig.optArray, {
    // eslint-disable-next-line indent
    dnsLookup: (address: any, callback: (arg0: null, arg1: any) => any) =>
      // eslint-disable-next-line indent, implicit-arrow-linebreak
      callback(null, address),
    scaleReads: 'slave'
  })
  : new Redis(redisConfig.opts);

redis.on('connect', () => {
  appLogger.info('Connected to redis instance');
  console.log('Connected to redis instance');
});

redis.on('ready', () => {
  appLogger.info('Redis instance is ready (data loaded from disk)');
});

// Handles redis connection temporarily going down without app crashing
// If an error is handled here, then redis will attempt to retry the request based on maxRetriesPerRequest
redis.on('error', (e: any) => {
  errorLogger.error(`Error connecting to redis: "${e.message}"`);
  console.log('xxxerror', e);
  if (e.message === 'ERR invalid password') {
    errorLogger.error(`Fatal error occurred "${e.message}". Stopping server.`);
    // throw e; // Fatal error, don't attempt to fix
  }
});

// 加了secs表示设置过期时间, 没加表示不过期 secs是秒数
export const setRedisByKey = function (key: any, value: any, secs?: any) {
  return new Promise((resolve, reject) => {
    const val = JSON.stringify(value);
    if (secs && secs > 0 && redis) {
      redis.set(key, val, 'EX', secs, (err, res) => {
        if (err) {
          errorLogger.error(
            `[Redis] set failed. key=[${key}], value=[${val}], seconds=[${secs}], err=[${err.message}]`
          );
          reject(err);
        } else {
          appLogger.info(
            `[Redis] set key=[${key}], value=[${val}], seconds=[${secs}], res=[${res}]`
          );
          resolve(res);
        }
      });
    } else {
      redis.set(key, val, (err, res) => {
        if (err) {
          errorLogger.error(
            `[Redis] set failed. key=[${key}], value=[${val}], err=[${err.message}]`
          );
          reject(err);
        } else {
          resolve(res);
        }
      });
    }
  });
};

// 加了secs表示设置过期时间, 没加表示不过期
export const setMulRedisByKey = async function (params: { key: string, value: any, secs?: any }[]) {
  return Promise.all(params.map(v => setRedisByKey(v.key, v.value, v.secs)));
};

// 原子自增
export const setRedisIncr = function (key: string) {
  return new Promise((resolve, reject) => {
    redis.incr(key, (err, res) => {
      if (err) {
        errorLogger.error(
          `[Redis] incr failed. key=[${key}], err=[${err.message}]`
        );
        reject(err);
      } else {
        appLogger.info(`[Redis] incr key=[${key}], res=[${res}]`);
        resolve(res);
      }
    });
  });
};
// 设置key的过期时间
export const expireRedisByKey = function (key: any, secs: any) {
  return new Promise((resolve, reject) => {
    redis.expire(key, secs, (err, res) => {
      if (err) {
        errorLogger.error(
          `[Redis] expire failed. key=[${key}], seconds=[${secs}], err=[${err.message}]`
        );
        reject(err);
      } else {
        appLogger.info(
          `[Redis] expire key=[${key}], seconds=[${secs}], res=[${res}]`
        );
        resolve(res);
      }
    });
  });
};

export const getRedisByKey = function (key: any) {
  return new Promise((resolve, reject) => {
    redis.get(key, (err, result: any) => {
      let res: any = result;
      if (err) {
        errorLogger.error(
          `[Redis] get failed. key=[${key}], err=[${err.message}]`
        );
        reject(err);
      } else {
        if (res !== 'null') {
          res = JSON.parse(result);
        } else {
          res = null;
        }
        resolve(res);
      }
    });
  });
};
// 删除单个的
export const delRedisByKey = (key: string) =>
  new Promise((resolve, reject) => {
    redis.del(key, (err, res) => {
      if (err) {
        errorLogger.error(
          `[Redis] del failed. key=[${key}], err=[${err.message}]`
        );
        reject(err);
      } else {
        appLogger.info(`[Redis] del key=[${key}], res=[${res}]`);
        resolve(res);
      }
    });
  });
// 批量删除
export const delRedisByMultipleKeys = (keys: string[]) => {
  if (keys.length) {
    const dels = keys.map(k => ['del', k]);
    return new Promise((resolve, reject) => {
      redis.pipeline(dels).exec((err, res) => {
        if (err) {
          errorLogger.error(
            `[Redis] del failed. key=[${JSON.stringify(keys)}], err=[${err.message
            }]`
          );
          reject(err);
        } else {
          appLogger.info(
            `[Redis] del key=[${JSON.stringify(keys)}], res=[${res}]`
          );
          resolve(res);
        }
      });
    });
  }
};

// 批量获取
export const getRedisByMultipleKeys = (keys: string[]) => {
  if (keys.length) {
    const gets = keys.map(k => ['get', k]);
    return new Promise((resolve, reject) => {
      redis.pipeline(gets).exec((err, res) => {
        if (err) {
          errorLogger.error(
            `[Redis] del failed. key=[${JSON.stringify(keys)}], err=[${err.message
            }]`
          );
          reject(err);
        } else {
          const data =
            Array.isArray(res) && res.length ? res.flat(Infinity) : [];
          const tmp = data.filter(v => v).map((v: any) => JSON.parse(v));
          appLogger.info(
            `[Redis] getRedisByMultipleKeys key=[${JSON.stringify(
              keys
            )}], res=[${JSON.stringify(tmp)}]`
          );
          resolve(tmp);
        }
      });
    });
  }
};

export const getRedisSetCountByValue = (key: string, value: string): Promise<number | undefined> => {
  return new Promise((resolve, reject) => {
    redis.sismember(key, value, (err, res) => {
      if (err) {
        errorLogger.error(
          `[Redis] scard failed. key=[${key}], err=[${err.message}]`
        );
        reject(err);
      } else {
        appLogger.info(
          `[Redis] sismember srem key=[${key}], value=[${value}], res=[${res}]`
        );
        resolve(res);
      }
    });
  });
};

export const delRedisSetCountByValue = (key: string, value: string) => {
  return new Promise((resolve, reject) => {
    redis.srem(key, value, (err, res) => {
      if (err) {
        errorLogger.error(
          `[Redis] scard srem key=[${key}], err=[${err.message}]`
        );
        reject(err);
      } else {
        appLogger.info(
          `[Redis] scard srem key=[${key}], value=[${value}], res=[${res}]`
        );
        resolve(res);
      }
    });
  });
};

export const existRedisKey = (key: string) => {
  return new Promise((resolve, reject) => {
    redis.exists(key, (err, res) => {
      if (err) {
        errorLogger.error(
          `[Redis] exists failed. key=[${key}], err=[${err.message}]`
        );
        reject(err);
      } else {
        appLogger.info(`[Redis] exists key=[${key}], res=[${res}]`);
        resolve(res);
      }
    });
  });
};

export const setRedisSetCountByValue = (
  key: string,
  value: string | string[]
) => {
  return new Promise((resolve, reject) => {
    if (Array.isArray(value)) {
      redis.sadd(key, ...value, (err, res) => {
        if (err) {
          errorLogger.error(
            `[Redis] sadd failed. key=[${key}], err=[${err.message}]`
          );
          reject(err);
        } else {
          resolve(res);
        }
      });
    } else {
      redis.sadd(key, value, (err, res) => {
        if (err) {
          errorLogger.error(
            `[Redis] sadd failed. key=[${key}], err=[${err.message}]`
          );
          reject(err);
        } else {
          resolve(res);
        }
      });
    }
  });
};

export const getRedisSetCountByMulValues = (key: string, values: string[]) => {
  return Promise.all(values.map(v => getRedisSetCountByValue(key, v)));
};
