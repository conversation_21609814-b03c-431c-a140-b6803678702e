/*
 * @Author: x<PERSON><PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-01-03 10:42:17
 * @LastEditors: ch<PERSON><PERSON><PERSON>
 * @LastEditTime: 2024-01-15 10:54:38
 * @Description: 数据库配置
 */
const config = Object.seal({
  port: 3004,
  database: {
    host: '*********',
    port: 3306,
    user: 'saas_user_w',
    password: 'afa28f72470948dc901aa1d48384d21c',
    database: 'db_saas',
    timezone: 'UTC'
  },
  emailConfig: {
    host: 'smtp.feishu.cn',
    port: 587,
    user: '<EMAIL>',
    pass: 'aX2o1Uk8U1rxS89i'
  },
  redisConfig: {
    is_cluster: false,
    session_key: 'RIXENGINE_SESSION_AUTHORIZATION_bdLhLhz4wfQD',
    tnt_host_prefix_key: 'Tenant_Host_Prefixs',
    get_report_csv_token_key: 'RIXENGINE_GET_PUBLIC_REPORT_CSV_FDTHWFDYW', // 1046租户提供的每日报表链接
    optArray: [
      {
        port: 6379,
        host: '**********',
        password: '',
        db: 0,
        family: 4 // IPV4/6
      }
    ],
    opts: {
      port: 6379,
      host: '**********',
      password: '',
      db: 0,
      family: 4 // IPV4/6
    },
    API_DAY_MAX_LIMIT: 50 // 每天限制访问次数
  }
});

export default config;
