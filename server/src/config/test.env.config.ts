/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-11-22 18:59:59
 * @LastEditors: chen<PERSON><PERSON>
 * @LastEditTime: 2024-01-15 10:58:35
 * @Description:
 */

const config = Object.seal({
  port: 3004,
  database: {
    host: '**********',
    port: 8306,
    user: 'test',
    password: '%xOakkb3',
    database: 'db_saas',
    timezone: 'UTC'
  },
  emailConfig: {
    host: 'smtp.feishu.cn',
    port: 587,
    user: '<EMAIL>',
    pass: 'aX2o1Uk8U1rxS89i'
  },
  redisConfig: {
    is_cluster: false,
    session_key: 'RIXENGINE_SESSION_AUTHORIZATION_bdLhLhz4wfQC',
    tnt_host_prefix_key: 'Tenant_Host_Prefixs',
    get_report_csv_token_key: 'RIXENGINE_GET_PUBLIC_REPORT_CSV_FDTHWFDYW', // 1046租户提供的每日报表链接
    optArray: [
      {
        port: 8379,
        host: '127.0.0.1',
        password: '',
        db: 0,
        family: 4 // IPV4/6
      }
    ],
    opts: {
      port: 8379,
      host: '127.0.0.1',
      password: '',
      db: 0,
      family: 4 // IPV4/6
    },
    API_DAY_MAX_LIMIT: 50 // 每天限制访问次数
  }
});

export default config;
