/*
 * @Author: x<PERSON><PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-01-13 14:30:12
 * @LastEditors: chen<PERSON><PERSON>
 * @LastEditTime: 2024-01-15 10:53:53
 * @Description: 数据库配置
 */

const config = Object.seal({
  port: 4092,
  database: {
    host: '127.0.0.1',
    port: 3306,
    user: 'root',
    password: '123456',
    database: 'db_saas',
    timezone: 'UTC'
  },
  emailConfig: {
    host: 'smtp.feishu.cn',
    port: 587,
    user: '<EMAIL>',
    pass: 'aX2o1Uk8U1rxS89i'
  },
  redisConfig: {
    is_cluster: false,
    session_key: 'RIXENGINE_SESSION_AUTHORIZATION_bdLhLhz4wfQC',
    tnt_host_prefix_key: 'Tenant_Host_Prefixs',
    get_report_csv_token_key: 'RIXENGINE_GET_PUBLIC_REPORT_CSV_FDTHWFDYW', // 1046租户提供的每日报表链接
    optArray: [
      {
        port: 6379,
        host: '127.0.0.1',
        password: '',
        db: 0,
        family: 4 // IPV4/6
      }
    ],
    opts: {
      port: 6379,
      host: '127.0.0.1',
      password: '',
      db: 0,
      family: 4 // IPV4/6
    },
    API_DAY_MAX_LIMIT: 50 // 每天限制访问次数
  }
});

export default config;
