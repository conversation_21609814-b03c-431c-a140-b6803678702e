/* *
 * @Author: yuan<PERSON>@algorix.co
 * @file:
 * @Date: 2019-03-28 20:41:41
 * @Last Modified by: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Last Modified time: 2022-11-23 18:01:02
 */
process.env.TZ = 'UTC';

import Koa from 'koa';
import koaBody from 'koa-body';
import compress from 'koa-compress';
import path from 'path';
import zlib from 'zlib';
// 注册文件别名
import moduleAlias from 'module-alias';

// 文件路径别名需要放置前面
moduleAlias.addAlias('@', path.join(__dirname, './'));

import { appLogger, errorLogger, httpLogger } from './config/log4js';
import { getTokenAndSidUrl } from './middleware/common';
import routers from './routers';
import { getConfig } from './utils';

const { port } = getConfig();

const app = new Koa();

app.use(
  koaBody({
    formLimit: '10mb',
    jsonLimit: '10mb',
    textLimit: '10mb',
    multipart: true // 是否支持 multipart-formdate 的表单
  })
);

// compress
app.use(
  compress({
    threshold: 2048, // 2kb
    gzip: {
      flush: zlib.constants.Z_SYNC_FLUSH
    },
    deflate: {
      flush: zlib.constants.Z_SYNC_FLUSH
    },
    br: false // disable brotli
  })
);

// 配置session中间件
// app.keys = ['c7d06befb6954a07b4333c472b3e7807']; // 加密的钥匙

// 挂载log
app.use(async (ctx, next) => {
  ctx.log = appLogger;
  const start = Date.now();
  await next();
  const responseTime = Date.now() - start;

  const { sid, token } = getTokenAndSidUrl(ctx);
  if (ctx.response.status !== 200) {
    httpLogger.error(
      `sid: ${sid}, token=${token}, host_prefix=[${
        ctx.state.host_prefix
      }] responseTime: ${(responseTime / 1000).toFixed(4)}s, requestUrl : ${
        ctx.request.url
      }, message:${ctx.message}`
    );
  } else {
    httpLogger.info(
      `sid: ${sid}, token=${token}, host_prefix=[${
        ctx.state.host_prefix
      }], session:  responseTime: ${(responseTime / 1000).toFixed(
        4
      )}s, requestUrl : ${ctx.request.url}, message:${ctx.message}`
    );
  }
});

// 初始化路由中间件
app.use(routers.routes());

// 监听启动端口
app.listen(port);
console.log(`the server is start at port ${port}`);
console.log(`current environment ${process.env.NODE_ENV}`);

// 未捕获的错误
process.on('uncaughtException', err => {
  console.error(err);
  errorLogger.error(`uncaughtException ${err.message}`);
});
// 未捕获的错误
process.on('unhandledRejection', (reason, p) => {
  console.error('Unhandled Rejection at: ', p);
  errorLogger.error(`unhandledRejection ${reason}`);
});
