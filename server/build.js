/**
 * @Author: ch<PERSON><PERSON><PERSON> chen<PERSON><PERSON>@algorix.co
 * @Date: 2023-05-15 18:46:15
 * @LastEditors: chenmudan <EMAIL>
 * @LastEditTime: 2023-05-15 18:46:17
 * @Description: 遍历打包后的dist js文件并去除所有console.log;
 */

const fs = require('fs');
const path = require('path');

// 匹配所有console打印日志
const ConsoleReg = /console\.(log|error|debug|dir|info)\(([\s\S]*?)\)(;?)/g;
// 输出文件位置
const BuildPath = path.resolve('./dist');

// 去除所有文件的console 递归读取
const removeAllConsole = (filePath) => {
  fs.readdirSync(filePath).forEach(async (file) => {
    const childPath = path.resolve(filePath, file);
    // 判断是否是文件夹
    if (fs.statSync(childPath).isDirectory()) {
      removeAllConsole(childPath);
    } else {
      // 读取文件并去除console
      const fileType = file.substring(file.lastIndexOf('.'));
      // 所有js文件去除console
      if (fileType === '.js') {
        const absolutePath = path.resolve(filePath, file);
        fs.readFile(absolutePath, 'utf-8', (err, data) => {
          if (err) {
            console.log(`remove console error from ${absolutePath}, err=[${err.message}]`);
          } else {
            // 需要把对应的console.log打印
            const str = data.replace(ConsoleReg, '');
            const consoleArr = data.match(ConsoleReg);
            fs.writeFileSync(absolutePath, str, 'utf-8');
            console.log(`remove console code from ${absolutePath}`, JSON.stringify(consoleArr));
          }
        });
      }
    }
  });
};

removeAllConsole(BuildPath);
