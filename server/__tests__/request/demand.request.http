# 功能：获取 某个 demand partner 的报表数据
# 这是 灰度环境的真实信息
# 文档：https://alltenants.console.rixengine.com/help/demandapi-v2
@protocol = https
@host = rix.rpt.rixengine.com
@url = {{protocol}}://{{host}}/demand/api/v2
@contentType = application/json
@userId = 32375
@authorization = 82de8e04e0e994c969fa2b9be48f93a4
@startDate = 2025-05-20
@endDate = 2025-05-24

### POST
curl -H "x-userid: {{userId}}"  -H "x-authorization: {{authorization}}" -H "Content-Type: {{contentType}}" \
-d '{"start_date":"{{startDate}}", "end_date":"{{endDate}}", "dimensions": ["day", "region"]}' \
-X POST "{{url}}"

### GET
curl -X GET "{{url}}?x-userid={{userId}}&x-authorization={{authorization}}&start_date={{startDate}}&end_date={{endDate}}&dimensions[]=day&dimensions[]=region&timezone=UTC+0"

curl -X GET "http://localhost:4092/demand/api/v2?x-userid=32375&x-authorization=82de8e04e0e994c969fa2b9be48f93a4&start_date=2025-05-20&end_date=2025-05-24&dimensions[]=day&dimensions[]=region&timezone=UTC+0"
