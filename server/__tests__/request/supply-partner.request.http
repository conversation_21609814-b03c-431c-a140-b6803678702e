# 功能：获取 当前 partner 所有相关的 supply partner 的报表数据
# 这是 灰度环境的真实信息
# 文档：https://alltenants.console.rixengine.com/help/supply-partner-api
@protocol = https
@host = rix.rpt.rixengine.com
@url = {{protocol}}://{{host}}/partner/api/supply
@contentType = application/json
@userId = 12177
@authorization = c0edaf50bfc283e86d2542de27b2ad44
@startDate = 2025-06-04
@endDate = 2025-06-05

### POST
curl -H "x-userid: {{userId}}"  -H "x-authorization: {{authorization}}" -H "Content-Type: {{contentType}}" \
-d '{"start_date":"{{startDate}}", "end_date":"{{endDate}}", "dimensions": ["day"]}' \
-X POST "{{url}}"

### GET
curl -X GET "{{url}}?x-userid={{userId}}&x-authorization={{authorization}}&start_date=2025-06-01&end_date=2025-06-07&dimensions[]=day&dimensions[]=day"
