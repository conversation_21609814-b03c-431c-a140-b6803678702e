# 功能：获取 租户的 日报表数据（目前只有 1046, 1050 两家租户有这个需求）
# 这是 生产环境的真实信息
# 文档：https://alltenants.console.rixengine.com/help/daily-csv-reporting-api
@protocol = https
@host = terabox.rpt.rixengine.com
@url = {{protocol}}://{{host}}/report/api/csv/v1
@contentType = application/json
@authorization = 9ee9fcab5942233541411e9008c005bf
@date = 2025-05-20

### POST
curl -H "x-authorization: {{authorization}}" -H "Content-Type: {{contentType}}" \
-d '{"date": ["{{date}}"]}' \
-X POST "{{url}}"

# 返回结果，需要下载文件的 url 和 过期时间
# {
#   "status": {
#     "code": 0,
#     "message": "success"
#   },
#   "timestamp": "Tue May 27 08:09:57 +00:00 2025",
#   "data": [
#     {
#       "path": "http://terabox.rpt.rixengine.com/report/api/file/N2IzNzVlM2NlNGMwMjg2NzQzODIzYTYwNDNkM2M1YzM6Njk5anJZNUFJUnhvaTFJMWZ3R0puanlqdTNwcm1JdEgzdDBDTlYrdlh1cnpWWDdaenA5dGUxcThuYVV0K2RkMGQ5OEZWQ20wM3JGSnFNYzVQaDltTUpTay9SNEtxMUZjUmFYVGtLc3ZNNXM9.csv",
#       "date": "2025-05-20",
#       "expire_time": "Tue May 27 09:09:57 +00:00 2025"
#     }
#   ]
# }
