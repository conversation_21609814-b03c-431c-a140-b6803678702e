# 功能：获取 某个 supply partner 的报表数据
# 这是 本地环境的信息
# 文档：https://alltenants.console.rixengine.com/help/sspapi-v2
@protocol = http
@host = localhost:4092
@url = {{protocol}}://{{host}}/ssp/api/v2
@contentType = application/json
@userId = 38155
@authorization = 4737b03b8e4b596818d7e4e2041bfaee
@startDate = 2025-05-20
@endDate = 2025-05-24

### POST
curl -H "x-userid: {{userId}}"  -H "x-authorization: {{authorization}}" -H "Content-Type: {{contentType}}" \
-d '{"start_date":"{{startDate}}", "end_date":"{{endDate}}", "dimensions": ["day"], "dimensionshttps://rpt.rtb.clickmobad.com/ssp/api/v2?x-userid":"38281"}' \
-X POST "{{url}}"

### GET
curl -X GET "{{url}}?x-userid={{userId}}&x-authorization={{authorization}}&start_date=2025-06-01&end_date=2025-06-07&dimensions[]=dayhttps://rpt.rtb.clickmobad.com/ssp/api/v2?x-userid=38283&x-authorization=2dce1bc560d3e92c2ecf9be5c08282c1&start_date=2025-06-01&end_date=2025-06-07&dimensions[]=day"
