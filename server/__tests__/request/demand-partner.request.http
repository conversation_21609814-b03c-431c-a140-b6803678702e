# 功能：获取 当前 partner 所有相关的 demand partner 的报表数据
# 这是 灰度环境的真实信息
# 文档：https://alltenants.console.rixengine.com/help/demand-partner-api
@protocol = https
@host = rix.rpt.rixengine.com
@url = {{protocol}}://{{host}}/partner/api/demand
@contentType = application/json
@userId = 12177
@authorization = 5df77a704d0392d0198d7e8475fea214
@startDate = 2025-05-20
@endDate = 2025-05-24

### POST
curl -H "x-userid: {{userId}}"  -H "x-authorization: {{authorization}}" -H "Content-Type: {{contentType}}" \
-d '{"start_date":"{{startDate}}", "end_date":"{{endDate}}", "dimensions": ["day"]}' \
-X POST "{{url}}"

### GET
curl -X GET "{{url}}?x-userid={{userId}}&x-authorization={{authorization}}&start_date={{startDate}}&end_date={{endDate}}&dimensions[]=day"
