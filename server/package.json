{"name": "server-ts", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"dev": "cross-env NODE_ENV=dev nodemon --config nodemon.json src/index.ts", "start": "node ./dist/index.js", "build": "rm -rf dist && tsc", "buildprod": "rm -rf dist && tsc && node ./build.js", "prod": "npm run build && npm run start", "pm2": "pm2 reload processes.json --only saas.rix-reporting-api --update-env", "pm2-test": "pm2 reload processes.json --only test.saas.rix-reporting-api --update-env", "test": "vitest"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@google-cloud/bigquery": "^6.2.0", "@google-cloud/storage": "^7.7.0", "@rixfe/rix-tools": "^1.8.0", "@types/qs": "^6.14.0", "axios": "^1.5.1", "ioredis": "^5.3.1", "joi": "^17.7.0", "koa": "^2.13.4", "koa-body": "^6.0.1", "koa-compress": "^5.1.0", "koa-router": "^12.0.0", "koa-session": "^6.2.0", "log4js": "^6.7.0", "module-alias": "^2.2.2", "moment": "^2.29.4", "moment-timezone": "^0.5.43", "mysql": "^2.18.1", "node-sql-parser": "^4.6.4", "nodemailer": "^6.8.0", "qs": "^6.14.0", "uuid": "^9.0.0"}, "devDependencies": {"@types/koa": "^2.13.5", "@types/koa-compress": "^4.0.3", "@types/koa-router": "^7.4.4", "@types/koa-session": "^5.10.6", "@types/module-alias": "^2.0.1", "@types/mysql": "^2.15.21", "@types/node": "^18.11.9", "@types/nodemailer": "^6.4.6", "@types/uuid": "^9.0.1", "@typescript-eslint/eslint-plugin": "^5.44.0", "@typescript-eslint/parser": "^5.44.0", "cross-env": "^7.0.3", "eslint": "^8.28.0", "eslint-config-airbnb": "^19.0.4", "eslint-plugin-import": "^2.26.0", "nodemon": "^2.0.20", "ts-node": "^10.9.1", "typescript": "^5.8.3", "vitest": "^3.2.4"}}