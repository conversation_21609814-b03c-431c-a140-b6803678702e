import path from 'path';
import { defineConfig } from 'vitest/config';

export default defineConfig({
  test: {
    globals: true,
    environment: 'node',
    exclude: ['node_modules', 'dist', 'coverage', 'logs', 'pids'],
    include: ['src/**/*.{test,spec}.ts', '__tests__/**/*.{test,spec}.ts']
  },
  // 解析配置
  resolve: {
    alias: {
      '@': path.resolve(__dirname, 'src')
    }
  },
  // 定义全局变量
  define: {
    'process.env.NODE_ENV': 'test'
  }
});
