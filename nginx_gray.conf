server {
    listen              8080;
    server_name *.rpt-n.rixengine.com rpt.*;
    underscores_in_headers on;

    location ~* /(\.svn|CVS|Entries){
        deny all;
    }

    location ~* /((.*)\.(.*)\/(.*)\.php){
        deny all;
    }

    location ~* /\.(sql|bak|inc|old|map)$ {
        deny all;
    }

    location /heartbeat {
        access_log off;
        default_type text/html;
        return 200 'rpt-api ok, I am alive...';
    }

    location / {
        proxy_pass http://127.0.0.1:3004;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
}
