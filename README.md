# Rix Reporting API

A Node.js reporting service providing data export and visualization APIs for the Rix platform.

## 项目概述

Rix Reporting API 是一个基于 Koa 和 TypeScript 构建的报表服务，提供报表数据导出和查询功能。

## 快速开始

### 开发环境配置

1. 克隆项目并安装依赖:

```bash
git clone ssh://*********************:21190/fe-saas/saas.rix-reporting-api.git
cd saas.rix-reporting-api/server
npm install
```

2. 启动开发服务器:

```bash
npm run dev
```

服务将在开发模式下运行，支持热重载。

### 项目脚本

- `npm run dev`: 开发模式运行 (使用 nodemon)
- `npm run build`: 构建 TypeScript 文件
- `npm run buildprod`: 生产环境构建 (包含额外优化)
- `npm run prod`: 构建并启动生产服务器
- `npm run pm2`: 使用 PM2 部署到生产环境
- `npm run pm2-test`: 使用 PM2 部署到测试环境

## 项目结构

项目遵循 MVC 架构模式:

- `server/src/index.ts`: 应用程序入口点
- `server/src/routers/`: API 路由定义
- `server/src/controllers/`: 请求处理器
- `server/src/services/`: 业务逻辑层
- `server/src/models/`: 数据访问层
- `server/src/middleware/`: Koa 中间件
- `server/src/db/`: 数据库连接和工具
- `server/src/config/`: 不同环境的配置文件
- `server/src/utils/`: 工具函数
- `server/src/types/`: TypeScript 类型定义
- `server/src/schema/`: 验证模式
- `server/src/constants/`: 常量定义

## 开发指南

### 代码规范

1. **路由结构**:
   - 路由定义在 `server/src/routers/` 目录下
   - 参考示例: `server/src/routers/reporting-api/report.ts`

2. **控制器模式**:
   - 控制器处理请求/响应逻辑
   - 参考示例: `server/src/controllers/reporting-api/report.ts`

3. **服务层**:
   - 业务逻辑在服务文件中
   - 参考示例: `server/src/services/reporting-api/report.ts`

4. **数据访问层**:
   - 模型文件处理数据库操作
   - 参考示例: `server/src/models/reporting-api/report.ts`

### 添加新功能

1. 在 `routers` 目录中添加新路由
2. 在 `controllers` 目录中创建相应的控制器
3. 在 `services` 目录中添加业务逻辑
4. 在 `models` 目录中添加数据访问方法
5. 根据需要添加类型定义和验证模式

## 部署流程

项目支持多种部署环境:
- **测试环境**: 用于测试目的
- **灰度环境**: 用于金丝雀/预生产部署
- **生产环境**: 用于生产环境

部署通过 `deploy.sh` 脚本管理:
```bash
./deploy.sh [test|prod]
```

此脚本会:
- 验证 Google Artifact Registry
- 安装依赖
- 构建项目
- 使用 PM2 启动应用程序

> 验证 Google Artifact Registry 之前需要 `gcloud auth application-default login` 完成认证

## 技术栈

- **Node.js**: JavaScript 运行时
- **TypeScript**: 类型安全的 JavaScript
- **Koa**: 轻量级 Web 框架
- **PM2**: Node.js 应用程序进程管理器
- **MySQL**: 存储报表数据的数据库
- **Redis**: 缓存和令牌存储
- **Google Cloud Platform**:
  - BigQuery 用于数据处理
  - Cloud Storage 用于文件存储
