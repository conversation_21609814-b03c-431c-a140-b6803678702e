server {
    listen              8080;
    server_name *.rpt.rixengine.com rpt.*;
    underscores_in_headers on;

    #set $cus_host $http_host;
    #if ($http_host = "demo.rpt.rixengine.com") {
    #    set $cus_host "demo.rpt.rixengine.com";
    #}

    set $subdomain default;
    if ($http_host ~* "^(.*)\.rpt\.rixengine\.com") {
        set $subdomain $1;
    }
    location ~* /(\.svn|CVS|Entries){
        deny all;
    }

    location ~* /((.*)\.(.*)\/(.*)\.php){
        deny all;
    }

    location ~* /\.(sql|bak|inc|old|map)$ {
        deny all;
    }

    location /heartbeat {
        access_log off;
        default_type text/html;
        return 200 'rpt-api ok, I am alive...';
    }

    location / {
        #proxy_set_header Host $cus_host;
        proxy_set_header Host $http_host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;

        if ($subdomain ~* 'show|demo|algorix|rix|rixad|iion') {
            proxy_pass http://**********:8080;
        }
        proxy_pass http://127.0.0.1:3004;
    }
}
